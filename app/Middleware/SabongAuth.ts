import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import User from 'App/Models/User'
import JwtService from 'App/Services/JwtService'

export default class SabongAuth {
  public async handle({ auth, response, request }: HttpContextContract, next: () => Promise<void>) {
    // code for middleware goes here. ABOVE THE NEXT CALL
    const authHeader = request.header('Authorization')
    let token: string | undefined

    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.split(' ')[1]
    }

    if (!token) {
      return response.status(401).json({ error: 'No authorization token provided' })
    }

    try {
      const payload = JwtService.verifyToken(token)

      if (!payload) return response.status(401).json({ error: 'Invalid or expired token' })

      const getUser = await User.firstOrCreate(
        {
          blockchainAddress: payload.address,
        },
        {
          blockchainAddress: payload.address,
        }
      )

      await auth.login(getUser)
      await next()
    } catch (error) {
      console.log(error)
      return response.status(400).json({ error: error.message || 'Something went wrong!' })
    }
  }
}
