import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { initializeContracts } from 'App/Helper/contracts'
import Chicken from 'App/Models/Chicken'
import Rental, {
  LegendaryFeatherBenefactorType,
  RentalStatus,
  RubStreakBenefactorType,
} from 'App/Models/Rental'
import RentalHistoryEvent from 'App/Models/RentalHistoryEvent'
import CreateRentalValidator from 'App/Validators/CreateRentalValidator'
import RentChickenValidator from 'App/Validators/RentChickenValidator'
import axios from 'axios'
import SABONG_CONFIG from 'Config/sabong'
import { DateTime } from 'luxon'
import { encodePacked, keccak256 } from 'viem'
import { signMessage } from 'viem/accounts'

export default class RentalsController {
  public async createRental({ auth, request, response }: HttpContextContract) {
    if (!auth.user) {
      response.status(401)
      return response.json({
        status: 0,
        message: 'Unauthorized',
      })
    }

    const {
      chickenTokenId,
      roninPrice,
      rentalPeriod,
      rewardDistribution,
      gameRewardDistribution,
      delegatedTask,
      sharedRewardAmount,
      renterAddress,
      rubStreakBenefactor,
      legendaryFeatherBenefactor,
      insurancePrice,
    } = await request.validate(CreateRentalValidator)

    // Check if chicken exists
    const chicken = await Chicken.findBy('tokenId', chickenTokenId)
    if (!chicken) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Chicken not found',
      })
    }

    // Check chicken ownership
    const { chickenLegacyContract, chickenGenesisContract } = initializeContracts()

    try {
      const chickenOwner =
        chickenTokenId > SABONG_CONFIG.CHICKEN_GENESIS_THRESHOLD
          ? await chickenLegacyContract.read.ownerOf([BigInt(chickenTokenId)])
          : await chickenGenesisContract.read.ownerOf([BigInt(chickenTokenId)])

      if (chickenOwner.toLowerCase() !== auth.user.blockchainAddress.toLowerCase()) {
        response.status(400)
        return response.json({
          status: 0,
          message: 'You do not own this chicken',
        })
      }
    } catch (error) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Failed to verify chicken ownership',
      })
    }

    // Check if chicken is already listed for rental
    const existingRental = await Rental.query()
      .where('chickenTokenId', chickenTokenId)
      .where('status', RentalStatus.AVAILABLE)
      .first()

    if (existingRental) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Chicken is already listed for rental',
      })
    }

    try {
      const metadata = await axios.get(
        `${SABONG_CONFIG.CHICKEN_IVORY_API_URL}/api/${chickenTokenId}`
      )

      const chickenState = metadata.data.attributes.find(
        (attr: any) => attr.trait_type === 'State'
      ).value

      if (chickenState !== 'Normal') {
        response.status(400)
        return response.json({
          status: 0,
          message: `Chicken is in ${chickenState} state`,
        })
      }

      const feathers = Number(
        metadata.data.attributes.find((attr: any) => attr.trait_type === 'Daily Feathers').value
      )

      if (sharedRewardAmount && sharedRewardAmount > feathers) {
        response.status(400)
        return response.json({
          status: 0,
          message: `Shared reward amount cannot be more than ${feathers}`,
        })
      }
    } catch (error) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Failed to fetch chicken metadata',
      })
    }

    if (renterAddress && roninPrice === '0') {
      const rental = await Rental.create({
        chickenTokenId,
        ownerAddress: auth.user.blockchainAddress,
        renterAddress,
        roninPrice: BigInt(roninPrice),
        rentalPeriod,
        rentedAt: DateTime.now(),
        expiresAt: DateTime.now().plus({ seconds: rentalPeriod }),
        status: RentalStatus.PENDING,
        insurancePrice: BigInt(insurancePrice || 0),
        signature: null,
        rewardDistribution: rewardDistribution || 1, // Default to DELEGATOR_ONLY})
        gameRewardDistribution: gameRewardDistribution || 1, // Default to DELEGATOR_ONLY
        delegatedTask: delegatedTask || 3, // Default to BOTH
        sharedRewardAmount: rewardDistribution === 3 ? sharedRewardAmount : null,
        rubStreakBenefactor: rubStreakBenefactor || RubStreakBenefactorType.DELEGATOR,
        legendaryFeatherBenefactor:
          legendaryFeatherBenefactor || LegendaryFeatherBenefactorType.DELEGATOR,
      })

      const charHash =
        keccak256(
          encodePacked(
            ['uint256', 'uint256', 'uint256', 'uint256', 'uint256', 'address'],
            [
              BigInt(chickenTokenId),
              BigInt(rental.id),
              BigInt(roninPrice),
              BigInt(insurancePrice || 0),
              BigInt(rentalPeriod),
              auth.user.blockchainAddress as `0x${string}`,
            ]
          )
        ) || ''

      const signature = await signMessage({
        message: { raw: charHash },
        privateKey: SABONG_CONFIG.SIGNER_KEY,
      })

      return response.json({
        status: 1,
        message: 'Chicken delegation pending verification onchain',
        data: {
          chickenTokenId,
          rentalId: rental.id,
          roninPrice: rental.roninPrice,
          insurancePrice: rental.insurancePrice,
          rentalPeriod: rental.rentalPeriod,
          ownerAddress: auth.user.blockchainAddress,
          signature,
        },
      })
    }

    if (!renterAddress && roninPrice !== '0') {
      // Create rental listing
      const rental = await Rental.create({
        chickenTokenId,
        ownerAddress: auth.user.blockchainAddress,
        renterAddress: null,
        roninPrice: BigInt(roninPrice),
        rentalPeriod,
        rentedAt: null,
        expiresAt: null,
        status: RentalStatus.PENDING,
        insurancePrice: BigInt(insurancePrice || 0),
        signature: null,
        rewardDistribution: rewardDistribution || 1, // Default to DELEGATOR_ONLY
        gameRewardDistribution: gameRewardDistribution || 1, // Default to DELEGATOR_ONLY
        delegatedTask: delegatedTask || 3, // Default to BOTH
        sharedRewardAmount: rewardDistribution === 3 ? sharedRewardAmount : null,
        rubStreakBenefactor: rubStreakBenefactor || RubStreakBenefactorType.DELEGATOR,
        legendaryFeatherBenefactor:
          legendaryFeatherBenefactor || LegendaryFeatherBenefactorType.DELEGATOR,
      })

      const charHash =
        keccak256(
          encodePacked(
            ['uint256', 'uint256', 'uint256', 'uint256', 'uint256', 'address'],
            [
              BigInt(chickenTokenId),
              BigInt(rental.id),
              BigInt(roninPrice),
              BigInt(insurancePrice || 0),
              BigInt(rentalPeriod),
              auth.user.blockchainAddress as `0x${string}`,
            ]
          )
        ) || ''

      const signature = await signMessage({
        message: { raw: charHash },
        privateKey: SABONG_CONFIG.SIGNER_KEY,
      })

      return response.json({
        status: 1,
        message: 'Rental listing pending verification onchain',
        data: {
          chickenTokenId,
          rentalId: rental.id,
          roninPrice: rental.roninPrice,
          insurancePrice: rental.insurancePrice,
          rentalPeriod: rental.rentalPeriod,
          ownerAddress: auth.user.blockchainAddress,
          signature,
        },
      })
    }
  }

  public async listAvailableRentals({ request, response }: HttpContextContract) {
    const {
      page = 1,
      pageSize = 10,
      search,
      minPrice,
      maxPrice,
      minDuration,
      maxDuration,
      featherRewardDistribution,
      gameRewardDistribution,
      delegatedTask,
      sortBy = 'created_at',
      sortOrder = 'desc',
    } = request.qs()

    // Build the query
    const query = Rental.query().where('status', RentalStatus.AVAILABLE)

    // Apply search filter (token ID or owner address)
    if (search) {
      const searchTerm = search.toString().toLowerCase()
      query.where((subQuery) => {
        subQuery
          .where('chicken_token_id', 'like', `%${searchTerm}%`)
          .orWhere('owner_address', 'like', `%${searchTerm}%`)
      })
    }

    // Apply price range filter (convert daily rate to total price)
    if (minPrice || maxPrice) {
      query.where((subQuery) => {
        if (minPrice) {
          // minPrice is daily rate in RON, convert to wei and calculate minimum total price
          const minPriceWei = (parseFloat(minPrice) * 1e18).toString()
          subQuery.whereRaw('(CAST(ronin_price AS DECIMAL(65,0)) / (rental_period / 86400)) >= ?', [
            minPriceWei,
          ])
        }
        if (maxPrice) {
          // maxPrice is daily rate in RON, convert to wei and calculate maximum total price
          const maxPriceWei = (parseFloat(maxPrice) * 1e18).toString()
          subQuery.whereRaw('(CAST(ronin_price AS DECIMAL(65,0)) / (rental_period / 86400)) <= ?', [
            maxPriceWei,
          ])
        }
      })
    }

    // Apply duration range filter (convert days to seconds)
    if (minDuration) {
      const minDurationSeconds = parseInt(minDuration) * 86400
      query.where('rental_period', '>=', minDurationSeconds)
    }
    if (maxDuration) {
      const maxDurationSeconds = parseInt(maxDuration) * 86400
      query.where('rental_period', '<=', maxDurationSeconds)
    }

    // Apply feather reward distribution filter
    if (featherRewardDistribution) {
      const rewardTypes = featherRewardDistribution
        .split(',')
        .map((type: string) => parseInt(type.trim()))
      query.whereIn('reward_distribution', rewardTypes)
    }

    // Apply game reward distribution filter
    if (gameRewardDistribution) {
      const gameRewardTypes = gameRewardDistribution
        .split(',')
        .map((type: string) => parseInt(type.trim()))
      query.whereIn('game_reward_distribution', gameRewardTypes)
    }

    // Apply delegated task filter
    if (delegatedTask) {
      const taskTypes = delegatedTask.split(',').map((type: string) => parseInt(type.trim()))
      query.whereIn('delegated_task', taskTypes)
    }

    // Apply sorting
    const validSortFields = ['created_at', 'ronin_price', 'rental_period', 'chicken_token_id']
    const sortField = validSortFields.includes(sortBy) ? sortBy : 'created_at'
    const sortDirection = sortOrder === 'asc' ? 'asc' : 'desc'

    // Special handling for price sorting (sort by daily rate)
    if (sortBy === 'ronin_price') {
      query.orderByRaw(
        `(CAST(ronin_price AS DECIMAL(65,0)) / (rental_period / 86400)) ${sortDirection}`
      )
    } else {
      query.orderBy(sortField, sortDirection)
    }

    // Execute query with pagination
    const rentals = await query.paginate(page, pageSize)

    return response.json({
      status: 1,
      data: rentals,
    })
  }

  public async rentChicken({ auth, request, response }: HttpContextContract) {
    if (!auth.user) {
      response.status(401)
      return response.json({
        status: 0,
        message: 'Unauthorized',
      })
    }

    const { rentalId } = await request.validate(RentChickenValidator)

    // Find the rental
    const rental = await Rental.find(rentalId)
    if (!rental) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Rental not found',
      })
    }

    // Check if rental is available
    if (rental.status !== RentalStatus.AVAILABLE) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Rental is not available',
      })
    }

    // Check if renter is not the owner
    if (rental.ownerAddress.toLowerCase() === auth.user.blockchainAddress.toLowerCase()) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'You cannot rent your own chicken',
      })
    }

    //rentId, chickenId, ethPrice, insurancePrice, renterAddress, renterWallet, ownerAddress
    // Generate signature for the rental
    const charHash =
      keccak256(
        encodePacked(
          ['uint256', 'uint256', 'uint256', 'uint256', 'address', 'address', 'address'],
          [
            BigInt(rental.id),
            BigInt(rental.chickenTokenId),
            rental.roninPrice,
            rental.insurancePrice,
            auth.user.blockchainAddress as `0x${string}`,
            auth.user.blockchainAddress as `0x${string}`,
            rental.ownerAddress as `0x${string}`,
          ]
        )
      ) || ''

    const signature = await signMessage({
      message: { raw: charHash },
      privateKey: SABONG_CONFIG.SIGNER_KEY,
    })

    return response.json({
      status: 1,
      data: {
        rentalId: rental.id,
        chickenTokenId: rental.chickenTokenId,
        roninPrice: rental.roninPrice,
        insurancePrice: rental.insurancePrice,
        renterAddress: auth.user.blockchainAddress,
        renterWallet: auth.user.blockchainAddress,
        ownerAddress: rental.ownerAddress,
        signature,
      },
    })
  }

  public async myRentals({ auth, response }: HttpContextContract) {
    const { user } = auth

    if (!user) {
      response.status(401)
      return response.json({
        status: 0,
        message: 'Unauthorized',
      })
    }

    // Get rentals where user is owner
    // For marketplace listings (AVAILABLE), don't check expires_at
    // For rented chickens (RENTED), check expires_at
    const ownedRentals = await Rental.query()
      .where('ownerAddress', user.blockchainAddress)
      .andWhere((query) => {
        query.where('status', RentalStatus.AVAILABLE).orWhere((subQuery) => {
          subQuery.where('status', RentalStatus.RENTED).whereRaw('expires_at > NOW()')
        })
      })

    // Get rentals where user is renter (only rented chickens, so always check expires_at)
    const rentedChickens = await Rental.query()
      .where('renterAddress', user.blockchainAddress)
      .where('status', RentalStatus.RENTED)
      .whereRaw('expires_at > NOW()')

    // Get expired rentals with unclaimed insurance where user is owner or renter
    const expiredRentalsWithInsurance = await Rental.query()
      .where((query) => {
        query
          .where('ownerAddress', user.blockchainAddress)
          .orWhere('renterAddress', user.blockchainAddress)
      })
      .where('status', RentalStatus.RENTED)
      .whereRaw('expires_at <= NOW()')
      .whereRaw('insurance_price > 0')
      .where('insuranceClaimed', false)
      .orderBy('expiresAt', 'desc')

    return response.json({
      status: 1,
      data: {
        ownedRentals,
        rentedChickens,
        expiredRentalsWithInsurance,
      },
    })
  }

  public async cancelRental({ auth, request, response }: HttpContextContract) {
    if (!auth.user) {
      response.status(401)
      return response.json({
        status: 0,
        message: 'Unauthorized',
      })
    }

    const { rentalId } = request.body()

    // Find the rental
    const rental = await Rental.find(rentalId)
    if (!rental) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Rental not found',
      })
    }

    // Check chicken ownership
    const { rentalContract } = initializeContracts()

    const rentOnchainInfo = await rentalContract.read.getRentalInfoBulk([[BigInt(rental.id)]])

    if (rentOnchainInfo.length > 0 && rentOnchainInfo[0].rentId === BigInt(0)) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Rental not found',
      })
    }

    const chickenOwnerOnchain = rentOnchainInfo[0].owner

    if (chickenOwnerOnchain.toLowerCase() !== auth.user.blockchainAddress.toLowerCase()) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'You are not the owner of this chicken',
      })
    }

    if (rental.status === RentalStatus.RENTED && rental.roninPrice !== BigInt(0)) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Rental is already rented and cannot be cancelled',
      })
    }

    const charHash =
      keccak256(
        encodePacked(
          ['uint256', 'uint256', 'address'],
          [
            BigInt(rental.id),
            BigInt(rental.chickenTokenId),
            auth.user.blockchainAddress as `0x${string}`,
          ]
        )
      ) || ''

    const signature = await signMessage({
      message: { raw: charHash },
      privateKey: SABONG_CONFIG.SIGNER_KEY,
    })

    return response.json({
      status: 1,
      message: 'Rental cancelling pending transaction onchain',
      data: {
        rentalId,
        chickenTokenId: rental.chickenTokenId,
        signature,
      },
    })
  }

  public async rentalHistory({ auth, request, response }: HttpContextContract) {
    const { user } = auth

    if (!user) {
      response.status(401)
      return response.json({
        status: 0,
        message: 'Unauthorized',
      })
    }

    const {
      page = 1,
      pageSize = 10,
      eventTypes,
      chickenTokenId,
      dateFrom,
      dateTo,
      actorAddress,
    } = request.qs()

    const fetchRentalHistory = await RentalHistoryEvent.query()
      .preload('rental')
      .where((subQuery) => {
        subQuery
          .whereHas('rental', (subQuery) => {
            subQuery
              .where('ownerAddress', user.blockchainAddress)
              .orWhere('renterAddress', user.blockchainAddress)
          })
          .orWhere('actorAddress', user.blockchainAddress)
      })
      .if(chickenTokenId, (query) => {
        query.whereHas('rental', (subQuery) => {
          subQuery.where('chickenTokenId', chickenTokenId)
        })
      })
      .if(dateFrom, (query) => {
        query.where('createdAt', '>=', dateFrom)
      })
      .if(dateTo, (query) => {
        query.where('createdAt', '<=', dateTo)
      })
      .if(actorAddress, (query) => {
        query.whereILike('actorAddress', `%${actorAddress}%`)
      })
      .if(eventTypes, (query) => {
        const eventTypeArray = eventTypes.split(',').map((type: string) => type.trim())
        query.whereIn('eventType', eventTypeArray)
      })
      .orderBy('createdAt', 'desc')
      .paginate(page, pageSize)

    return response.json({
      status: 1,
      data: fetchRentalHistory,
    })
  }

  public async getChickenRental({ request, response }: HttpContextContract) {
    const { chickenTokenId } = request.params()

    // Find active rental for the specific chicken (AVAILABLE or RENTED)
    // For marketplace listings (AVAILABLE), don't check expires_at
    // For rented chickens (RENTED), check expires_at
    const rental = await Rental.query()
      .where('chickenTokenId', chickenTokenId)
      .andWhere((query) => {
        query.where('status', RentalStatus.AVAILABLE).orWhere((subQuery) => {
          subQuery.where('status', RentalStatus.RENTED).whereRaw('expires_at > NOW()')
        })
      })
      .first()

    if (!rental) {
      return response.json({
        status: 0,
        data: null,
      })
    }

    return response.json({
      status: 1,
      data: rental,
    })
  }

  public async getChickenRentalsBulk({ request, response }: HttpContextContract) {
    const { chickenTokenIds } = request.body()

    // Find active rentals for the specific chickens (AVAILABLE or RENTED)
    // For marketplace listings (AVAILABLE), don't check expires_at
    // For rented chickens (RENTED), check expires_at
    const rentals = await Rental.query()
      .whereIn('chickenTokenId', chickenTokenIds)
      .andWhere((query) => {
        query.where('status', RentalStatus.AVAILABLE).orWhere((subQuery) => {
          subQuery.where('status', RentalStatus.RENTED).whereRaw('expires_at > NOW()')
        })
      })

    // Create a mapping of chickenTokenId -> rental data
    const rentalMap: Record<number, any> = {}

    // Initialize all requested chickens with null (no rental)
    chickenTokenIds.forEach((tokenId: number) => {
      rentalMap[tokenId] = null
    })

    // Fill in the actual rental data for chickens that have rentals
    rentals.forEach((rental) => {
      rentalMap[rental.chickenTokenId] = rental
    })

    return response.json({
      status: 1,
      data: rentalMap,
    })
  }

  public async getChickenRentalsByWallet({ request, response }: HttpContextContract) {
    const { walletAddress } = request.qs()

    // Find active rentals for the specific wallet address (as owner)
    const rentals = await Rental.query()
      .where('renterAddress', walletAddress)
      .where('status', RentalStatus.RENTED)
      .whereRaw('expires_at > NOW()')
      .orderBy('expiresAt', 'asc')

    const rentalsMetadata = [] as any[]
    try {
      for (const rental of rentals) {
        const metadata = await axios.get(
          `${SABONG_CONFIG.CHICKEN_IVORY_API_URL}/api/${rental.chickenTokenId}`
        )
        let cleanedMetadata = {
          delegatedTask: rental.delegatedTask,
          rewardDistribution: rental.rewardDistribution,
          gameRewardDistribution: rental.gameRewardDistribution,
          sharedRewardAmount: rental.sharedRewardAmount,
          renterAddress: walletAddress,
          ownerAddress: rental.ownerAddress,
          tokenId: metadata.data.edition,
          image: metadata.data.image,
          dailyFeathers:
            Number(
              metadata.data.attributes.find((attr: any) => attr.trait_type === 'Daily Feathers')
                .value
            ) || 0,
          legendaryCount:
            Number(
              metadata.data.attributes.find((attr: any) => attr.trait_type === 'Legendary Count')
                .value
            ) || 0,
        }
        rentalsMetadata.push(cleanedMetadata)
      }

      return response.json({
        status: 1,
        data: rentalsMetadata,
      })
    } catch (error) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Failed to fetch rentals',
      })
    }
  }

  public async createRentalBulk({ auth, request, response }: HttpContextContract) {
    if (!auth.user) {
      response.status(401)
      return response.json({
        status: 0,
        message: 'Unauthorized',
      })
    }

    const { rentals } = request.body()

    if (!Array.isArray(rentals) || rentals.length === 0) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Rentals array is required and cannot be empty',
      })
    }

    const { bulkFetcherContract } = initializeContracts()
    const results = [] as any

    // Get all owned chickens for the user at once
    const legacyChickenTokenIds = await bulkFetcherContract.read.getLegacyChickenTokenIdsOfAddress([
      auth.user.blockchainAddress as `0x${string}`,
    ])
    const genesisChickenTokenIds =
      await bulkFetcherContract.read.getGenesisChickenTokenIdsOfAddress([
        auth.user.blockchainAddress as `0x${string}`,
      ])

    const ownedChickenIds = new Set([
      ...legacyChickenTokenIds.map((id) => Number(id)),
      ...genesisChickenTokenIds.map((id) => Number(id)),
    ])

    for (const rentalData of rentals) {
      const {
        chickenTokenId,
        roninPrice,
        rentalPeriod,
        rewardDistribution,
        gameRewardDistribution,
        delegatedTask,
        sharedRewardAmount,
        renterAddress,
        rubStreakBenefactor,
        legendaryFeatherBenefactor,
        insurancePrice,
      } = rentalData

      try {
        // Check if chicken exists
        const chicken = await Chicken.findBy('tokenId', chickenTokenId)
        if (!chicken) {
          results.push({
            chickenTokenId,
            success: false,
            message: 'Chicken not found',
          })
          continue
        }

        // Check chicken ownership using bulk fetched data
        if (!ownedChickenIds.has(chickenTokenId)) {
          results.push({
            chickenTokenId,
            success: false,
            message: 'You do not own this chicken',
          })
          continue
        }

        // Check if chicken is already listed
        const existingRental = await Rental.query()
          .where('chickenTokenId', chickenTokenId)
          .where('status', RentalStatus.AVAILABLE)
          .first()

        if (existingRental) {
          results.push({
            chickenTokenId,
            success: false,
            message: 'Chicken is already listed for rental',
          })
          continue
        }

        // Create rental
        const rental = await Rental.create({
          chickenTokenId,
          ownerAddress: auth.user.blockchainAddress,
          renterAddress: renterAddress || null,
          roninPrice: BigInt(roninPrice),
          rentalPeriod,
          rentedAt: renterAddress && roninPrice === '0' ? DateTime.now() : null,
          expiresAt:
            renterAddress && roninPrice === '0'
              ? DateTime.now().plus({ seconds: rentalPeriod })
              : null,
          status: RentalStatus.PENDING,
          insurancePrice: BigInt(insurancePrice || 0),
          signature: null,
          rewardDistribution: rewardDistribution || 1,
          gameRewardDistribution: gameRewardDistribution || 1,
          delegatedTask: delegatedTask || 3,
          sharedRewardAmount: rewardDistribution === 3 ? sharedRewardAmount : null,
          rubStreakBenefactor: rubStreakBenefactor || RubStreakBenefactorType.DELEGATOR,
          legendaryFeatherBenefactor:
            legendaryFeatherBenefactor || LegendaryFeatherBenefactorType.DELEGATOR,
        })

        const charHash = keccak256(
          encodePacked(
            ['uint256', 'uint256', 'uint256', 'uint256', 'uint256', 'address'],
            [
              BigInt(chickenTokenId),
              BigInt(rental.id),
              BigInt(roninPrice),
              BigInt(insurancePrice || 0),
              BigInt(rentalPeriod),
              auth.user.blockchainAddress as `0x${string}`,
            ]
          )
        )

        const signature = await signMessage({
          message: { raw: charHash },
          privateKey: SABONG_CONFIG.SIGNER_KEY,
        })

        results.push({
          chickenTokenId,
          success: true,
          data: {
            chickenTokenId,
            rentalId: rental.id,
            roninPrice: rental.roninPrice.toString(),
            insurancePrice: rental.insurancePrice.toString(),
            rentalPeriod: rental.rentalPeriod,
            ownerAddress: auth.user.blockchainAddress,
            signature,
          },
        })
      } catch (error) {
        results.push({
          chickenTokenId,
          success: false,
          message: `Failed to create rental: ${error.message}`,
        })
      }
    }

    return response.json({
      status: 1,
      message: 'Bulk rental creation completed',
      data: results,
    })
  }

  public async cancelRentalBulk({ auth, request, response }: HttpContextContract) {
    if (!auth.user) {
      response.status(401)
      return response.json({
        status: 0,
        message: 'Unauthorized',
      })
    }

    const { rentalIds } = request.body()

    if (!Array.isArray(rentalIds) || rentalIds.length === 0) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Rental IDs array is required and cannot be empty',
      })
    }

    const { rentalContract } = initializeContracts()
    const results = [] as any

    for (const rentalId of rentalIds) {
      try {
        // Find the rental
        const rental = await Rental.find(rentalId)
        if (!rental) {
          results.push({
            rentalId,
            success: false,
            message: 'Rental not found',
          })
          continue
        }

        // Check onchain info
        const rentOnchainInfo = await rentalContract.read.getRentalInfoBulk([[BigInt(rental.id)]])

        if (rentOnchainInfo.length > 0 && rentOnchainInfo[0].rentId === BigInt(0)) {
          results.push({
            rentalId,
            success: false,
            message: 'Rental not found onchain',
          })
          continue
        }

        const chickenOwnerOnchain = rentOnchainInfo[0].owner

        if (chickenOwnerOnchain.toLowerCase() !== auth.user.blockchainAddress.toLowerCase()) {
          results.push({
            rentalId,
            success: false,
            message: 'You are not the owner of this chicken',
          })
          continue
        }

        if (rental.status === RentalStatus.RENTED && rental.roninPrice !== BigInt(0)) {
          results.push({
            rentalId,
            success: false,
            message: 'Rental is already rented and cannot be cancelled',
          })
          continue
        }

        const charHash = keccak256(
          encodePacked(
            ['uint256', 'uint256', 'address'],
            [
              BigInt(rental.id),
              BigInt(rental.chickenTokenId),
              auth.user.blockchainAddress as `0x${string}`,
            ]
          )
        )

        const signature = await signMessage({
          message: { raw: charHash },
          privateKey: SABONG_CONFIG.SIGNER_KEY,
        })

        results.push({
          rentalId,
          success: true,
          data: {
            rentalId: rental.id,
            chickenTokenId: rental.chickenTokenId,
            signature,
          },
        })
      } catch (error) {
        results.push({
          rentalId,
          success: false,
          message: `Failed to cancel rental: ${error.message}`,
        })
      }
    }

    return response.json({
      status: 1,
      message: 'Bulk rental cancellation completed',
      data: results,
    })
  }
}
