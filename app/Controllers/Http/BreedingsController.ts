import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { initializeContracts } from 'App/Helper/contracts'
import BreedingCooldown from 'App/Models/BreedingCooldown'
import BreedingFee from 'App/Models/BreedingFee'
import Chicken, { EChickenIsHatchedStatus } from 'App/Models/Chicken'
import InitiateBreedingValidator from 'App/Validators/InitiateBreedingValidator'
import InitiateBulkBreedingValidator from 'App/Validators/InitiateBulkBreedingValidator'
import SABONG_CONFIG, { EBreedingItem } from 'Config/sabong'
import { syncMetadata } from './../../../feature/hashlips/generator'
import { DateTime } from 'luxon'
import { encodeAbiParameters, encodePacked, formatUnits, keccak256, parseEther } from 'viem'
import { signMessage } from 'viem/accounts'
import {
  recalculateBreedingCooldown,
  recalculateBreedingFee,
  rerollInnatePoints,
} from 'App/Helper/chicken'
import BreedEvent from 'App/Models/BreedEvent'
import { incrementParentBreedCount } from 'App/Services/ProcessBreedEventService'
import fs from 'fs'
import Application from '@ioc:Adonis/Core/Application'

export default class BreedingsController {
  public async maintenanceBreeding({ response }: HttpContextContract) {
    response.status(400)
    return response.json({
      status: 0,
      message: 'Breeding is currently unavailable.',
    })
  }

  public async initiateBreeding({ request, response }: HttpContextContract) {
    const { address, chickenLeftTokenId, chickenRightTokenId, resources } = await request.validate(
      InitiateBreedingValidator
    )

    const {
      chickenLegacyContract,
      chickenGenesisContract,
      itemContract,
      cockContract,
      breedingContract,
      resourcesContract,
    } = initializeContracts()

    // Validate Breeding Items
    const leftParentItems: number[] =
      resources
        ?.filter((item: number[]) => Number(item?.[0]) === 0)
        .map((item: number[]) => Number(item?.[1])) || []

    const bothParentsItems: number[] =
      resources
        ?.filter((item: number[]) => Number(item?.[0]) === 1)
        .map((item: number[]) => Number(item?.[1])) || []

    const rightParentItems: number[] =
      resources
        ?.filter((item: number[]) => Number(item?.[0]) === 2)
        .map((item: number[]) => Number(item?.[1])) || []

    const allowedLeftRightParentItems = [
      EBreedingItem.SOULKNOT,
      EBreedingItem.RANDOMIZER,
      EBreedingItem.GREGORS_GIFT,
      EBreedingItem.MENDELS_MEMENTO,
      EBreedingItem.QUENTINS_TALON,
      EBreedingItem.DRAGONS_WHIP,
      EBreedingItem.CHIBIDEIS_CURSE,
      EBreedingItem.ALL_SEEING_SEED,
      EBreedingItem.CHIM_LACS_CURIO,
      EBreedingItem.SUAVE_SCISSORS,
      EBreedingItem.SIMURGHS_SOVEREIGN,
      EBreedingItem.ST_ELMO_FIRE,
      EBreedingItem.RAINBOW_RAIN,
      EBreedingItem.DIPS_BEAK,
      EBreedingItem.POS2_PELLET,
      EBreedingItem.FETZZZ_FEET,
      EBreedingItem.VANDERERENS_VITALITY,
      EBreedingItem.PINONGS_BIRD,
      EBreedingItem.OUCHIES_ORNAMENT,
      EBreedingItem.LOCKED_IN_STATE,
    ]

    const allowedBothParentsItems = [
      EBreedingItem.SELENIUM,
      EBreedingItem.SELENIUM_PRO,
      EBreedingItem.COMMON_INCUBATOR,
      EBreedingItem.ARCANE_INCUBATOR,
    ]

    const invalidLeftParentItems = leftParentItems.filter(
      (item) => !allowedLeftRightParentItems.includes(item)
    )

    const invalidRightParentItems = rightParentItems.filter(
      (item) => !allowedLeftRightParentItems.includes(item)
    )

    const invalidBothParentsItems = bothParentsItems.filter(
      (item) => !allowedBothParentsItems.includes(item)
    )

    if (invalidLeftParentItems.length > 0) {
      response.status(400)
      return response.json({
        status: 0,
        message: `Invalid breeding item for left parent: ${invalidLeftParentItems.join(', ')}`,
      })
    }

    if (invalidRightParentItems.length > 0) {
      response.status(400)
      return response.json({
        status: 0,
        message: `Invalid breeding item for right parent: ${invalidRightParentItems.join(', ')}`,
      })
    }

    if (invalidBothParentsItems.length > 0) {
      response.status(400)
      return response.json({
        status: 0,
        message: `Invalid breeding item for both parents: ${invalidBothParentsItems.join(', ')}`,
      })
    }

    // Check parent ownership
    const chickenLeftTokenIdOwner =
      chickenLeftTokenId > SABONG_CONFIG.CHICKEN_GENESIS_THRESHOLD
        ? await chickenLegacyContract.read.ownerOf([BigInt(chickenLeftTokenId)])
        : await chickenGenesisContract.read.ownerOf([BigInt(chickenLeftTokenId)])

    if (address.toLowerCase() !== chickenLeftTokenIdOwner.toLowerCase()) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'chickenLeftTokenId is not owned by the owner wallet address',
      })
    }

    const checkChickenLeftIsHatched = await Chicken.query()
      .where('tokenId', chickenLeftTokenId)
      .where('isHatched', EChickenIsHatchedStatus.YES)
      .first()

    if (!checkChickenLeftIsHatched) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'chickenLeftTokenId is not yet hatched!',
      })
    }

    const chickenRightTokenIdOwner =
      chickenRightTokenId > SABONG_CONFIG.CHICKEN_GENESIS_THRESHOLD
        ? await chickenLegacyContract.read.ownerOf([BigInt(chickenRightTokenId)])
        : await chickenGenesisContract.read.ownerOf([BigInt(chickenRightTokenId)])

    if (address.toLowerCase() !== chickenRightTokenIdOwner.toLowerCase()) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'chickenRightTokenId is not owned by the owner wallet address',
      })
    }

    const checkChickenRightIsHatched = await Chicken.query()
      .where('tokenId', chickenRightTokenId)
      .where('isHatched', EChickenIsHatchedStatus.YES)
      .first()

    if (!checkChickenRightIsHatched) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'chickenRightTokenId is not yet hatched!',
      })
    }

    // Check breeding cooldown
    const parentBreedingCooldown = (
      await breedingContract.read.getChickenBreedTimeBatch([
        [BigInt(chickenLeftTokenId), BigInt(chickenRightTokenId)],
      ])
    ).map((item) => Number(item))

    const isChickenLeftTokenIdDoneCooldown =
      parentBreedingCooldown[0] * 1000 <= DateTime.now().toMillis()

    if (!isChickenLeftTokenIdDoneCooldown) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'chickenLeftTokenId is not done with its breeding cooldown',
      })
    }

    const isChickenRightTokenIdDoneCooldown =
      parentBreedingCooldown[1] * 1000 <= DateTime.now().toMillis()

    if (!isChickenRightTokenIdDoneCooldown) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'ChickenRightTokenId is not done with its breeding cooldown',
      })
    }

    const parentBreedingCount = (
      await breedingContract.read.getChickenBreedCountBatch([
        [BigInt(chickenLeftTokenId), BigInt(chickenRightTokenId)],
      ])
    ).map((item) => Number(item))

    const chickenLeftBreedingCount = parentBreedingCount[0]

    const chickenRightBreedingCount = parentBreedingCount[1]

    const getChickenLeftTokenIdCooldownDuration = await BreedingCooldown.query()
      .where(
        'count',
        chickenLeftBreedingCount >= SABONG_CONFIG.BREEDING_COOLDOWN_MAX_COUNT_DURATION
          ? SABONG_CONFIG.BREEDING_COOLDOWN_MAX_COUNT_DURATION
          : chickenLeftBreedingCount
      )
      .first()

    if (!getChickenLeftTokenIdCooldownDuration) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'chickenLeftTokenId does not have a valid breeding cooldown duration',
      })
    }

    const getChickenRightTokenIdCooldownDuration = await BreedingCooldown.query()
      .where(
        'count',
        chickenRightBreedingCount >= SABONG_CONFIG.BREEDING_COOLDOWN_MAX_COUNT_DURATION
          ? SABONG_CONFIG.BREEDING_COOLDOWN_MAX_COUNT_DURATION
          : chickenRightBreedingCount
      )
      .first()

    if (!getChickenRightTokenIdCooldownDuration) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'chickenRightTokenId does not have a valid breeding cooldown duration',
      })
    }

    const chickenLeftTokenIdCooldownDuration = recalculateBreedingCooldown(
      chickenLeftBreedingCount,
      getChickenLeftTokenIdCooldownDuration
    )

    const chickenRightTokenIdCooldownDuration = recalculateBreedingCooldown(
      chickenRightBreedingCount,
      getChickenRightTokenIdCooldownDuration
    )

    const totalBreedingCooldown =
      chickenLeftTokenIdCooldownDuration.cooldown + chickenRightTokenIdCooldownDuration.cooldown

    // calculate breeding fee
    const getChickenLeftTokenIdBreedingFee = await BreedingFee.query()
      .where(
        'count',
        chickenLeftBreedingCount >= SABONG_CONFIG.BREEDING_COOLDOWN_MAX_COUNT_FEE
          ? SABONG_CONFIG.BREEDING_COOLDOWN_MAX_COUNT_FEE
          : chickenLeftBreedingCount
      )
      .first()

    if (!getChickenLeftTokenIdBreedingFee) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'chickenLeftTokenId does not have a valid breeding fee',
      })
    }

    const getChickenRightTokenIdBreedingFee = await BreedingFee.query()
      .where(
        'count',
        chickenRightBreedingCount >= SABONG_CONFIG.BREEDING_COOLDOWN_MAX_COUNT_FEE
          ? SABONG_CONFIG.BREEDING_COOLDOWN_MAX_COUNT_FEE
          : chickenRightBreedingCount
      )
      .first()

    if (!getChickenRightTokenIdBreedingFee) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'chickenRightTokenId does not have a valid breeding fee',
      })
    }

    const chickenLeftTokenIdBreedingFee = recalculateBreedingFee(
      chickenLeftBreedingCount,
      getChickenLeftTokenIdBreedingFee
    )

    const chickenRightTokenIdBreedingFee = recalculateBreedingFee(
      chickenRightBreedingCount,
      getChickenRightTokenIdBreedingFee
    )

    const breedingCockFee = chickenLeftTokenIdBreedingFee.cock + chickenRightTokenIdBreedingFee.cock
    const breedingFeatherFee =
      chickenLeftTokenIdBreedingFee.feathers + chickenRightTokenIdBreedingFee.feathers

    const totalAmount = breedingCockFee
    const amountToVault =
      ((100 - SABONG_CONFIG.BREEDING_NINUNO_PERCENTAGE_PER_PARENT * 2) / 100) * breedingCockFee

    const amountToNinuno =
      (SABONG_CONFIG.BREEDING_NINUNO_PERCENTAGE_PER_PARENT / 100) * breedingCockFee * 2

    const feathers = [[1, breedingFeatherFee]]

    let breedingCooldownTime = totalBreedingCooldown * 24 * 60 * 60 // days to seconds

    // Check Cock Balance

    const checkCockBalanceRaw = await cockContract.read.balanceOf([address as `0x${string}`])
    const checkCockDecimals = await cockContract.read.decimals()
    const checkCockBalance = Number(formatUnits(checkCockBalanceRaw, checkCockDecimals))

    if (breedingCockFee > checkCockBalance) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Insufficient $COCK balance',
      })
    }

    // Check Feather Balance

    const checkFeatherBalance = Number(
      await itemContract.read.balanceOf([address as `0x${string}`, BigInt(1)])
    )

    if (breedingFeatherFee > checkFeatherBalance) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Insufficient FEATHER balance',
      })
    }

    // Check Resources Balance

    if (resources && resources.length > 0) {
      const invalidResources = resources.filter((item: number[]) => item[2] !== 1)

      if (invalidResources.length > 0) {
        response.status(400)
        return response.json({
          status: 0,
          message: `Invalid resources amount: ${invalidResources
            .map((item) => item[1])
            .join(', ')}. Amount must be 1`,
        })
      }

      const checkResourcesBalance = await resourcesContract.read.balanceOfBatch([
        Array(resources.length).fill(address as `0x${string}`),
        [...resources.map((item: number[]) => BigInt(item?.[1]))],
      ])

      const checkResourcesBalanceFormatted = checkResourcesBalance.map((item) =>
        Number(formatUnits(item, 0))
      )

      const checkResourcesBalanceFormattedFiltered = checkResourcesBalanceFormatted.filter(
        (item, index) => item < resources[index][2]
      )

      if (checkResourcesBalanceFormattedFiltered.length > 0) {
        response.status(400)
        return response.json({
          status: 0,
          message: `Insufficient resources balance: ${checkResourcesBalanceFormattedFiltered
            .map((item, index) => `${resources[index][1]}: ${item}`)
            .join(', ')}`,
        })
      }
    }

    // Apply Selenium and Selenium Pro if exists
    if (bothParentsItems.includes(EBreedingItem.SELENIUM)) {
      breedingCooldownTime *= 0.75
    }

    if (bothParentsItems.includes(EBreedingItem.SELENIUM_PRO)) {
      breedingCooldownTime = 0
    }

    const charHash =
      keccak256(
        encodePacked(
          [
            'address', //sender
            'uint256', //chickenLeftTokenId
            'uint256', //chickenRightTokenId
            'uint256', //totalAmount
            'uint256', //amountToVault
            'uint256', //amountToNinuno
            'uint256', //breedingTime
            'bytes32', //feathersHash
            'bytes32', //resourcesHash
          ],
          [
            address as `0x${string}`,
            BigInt(chickenLeftTokenId),
            BigInt(chickenRightTokenId),
            parseEther(`${totalAmount}`),
            parseEther(`${amountToVault}`),
            parseEther(`${amountToNinuno}`),
            BigInt(breedingCooldownTime),
            keccak256(
              encodeAbiParameters(
                [{ type: 'uint256[][]' }],
                [feathers.map((arr) => arr.map((n) => BigInt(n)))]
              )
            ),
            keccak256(
              encodeAbiParameters(
                [{ type: 'uint256[][]' }],
                [resources ? resources.map((arr) => arr.map((n) => BigInt(n))) : []]
              )
            ),
          ]
        )
      ) || ''

    const signature = await signMessage({
      message: { raw: charHash },
      privateKey: SABONG_CONFIG.SIGNER_KEY,
    })

    return response.json({
      status: 1,
      data: {
        address,
        chickenLeftTokenId: BigInt(chickenLeftTokenId),
        chickenRightTokenId: BigInt(chickenRightTokenId),
        totalAmount: parseEther(`${totalAmount}`),
        amountToVault: parseEther(`${amountToVault}`),
        amountToNinuno: parseEther(`${amountToNinuno}`),
        feathersData: feathers.map((arr) => arr.map((n) => BigInt(n))),
        resourcesData: resources ? resources.map((arr) => arr.map((n) => BigInt(n))) : [],
        breedingCooldownTime: BigInt(breedingCooldownTime),
        signature,
      },
    })
  }

  public async initiateBulkBreeding({ request, response }: HttpContextContract) {
    const { address, data } = await request.validate(InitiateBulkBreedingValidator)

    const {
      chickenLegacyContract,
      chickenGenesisContract,
      itemContract,
      cockContract,
      breedingContract,
      resourcesContract,
    } = initializeContracts()

    let chickenLeftTokenIds: number[] = []
    let chickenRightTokenIds: number[] = []
    let totalAmounts: number[] = []
    let amountsToVault: number[] = []
    let amountsToNinuno: number[] = []
    let breedingCooldownTimes: number[] = []
    let feathersData: number[][][] = []
    let resourcesData: number[][][] = []
    let signatures: string[] = []

    for (const { chickenLeftTokenId, chickenRightTokenId, resources } of data) {
      const leftParentItems: number[] =
        resources
          ?.filter((item: number[]) => Number(item?.[0]) === 0)
          .map((item: number[]) => Number(item?.[1])) || []

      const bothParentsItems: number[] =
        resources
          ?.filter((item: number[]) => Number(item?.[0]) === 1)
          .map((item: number[]) => Number(item?.[1])) || []

      const rightParentItems: number[] =
        resources
          ?.filter((item: number[]) => Number(item?.[0]) === 2)
          .map((item: number[]) => Number(item?.[1])) || []

      const allowedLeftRightParentItems = [
        EBreedingItem.SOULKNOT,
        EBreedingItem.RANDOMIZER,
        EBreedingItem.GREGORS_GIFT,
        EBreedingItem.MENDELS_MEMENTO,
        EBreedingItem.QUENTINS_TALON,
        EBreedingItem.DRAGONS_WHIP,
        EBreedingItem.CHIBIDEIS_CURSE,
        EBreedingItem.ALL_SEEING_SEED,
        EBreedingItem.CHIM_LACS_CURIO,
        EBreedingItem.SUAVE_SCISSORS,
        EBreedingItem.SIMURGHS_SOVEREIGN,
        EBreedingItem.ST_ELMO_FIRE,
        EBreedingItem.RAINBOW_RAIN,
        EBreedingItem.DIPS_BEAK,
        EBreedingItem.POS2_PELLET,
        EBreedingItem.FETZZZ_FEET,
        EBreedingItem.VANDERERENS_VITALITY,
        EBreedingItem.PINONGS_BIRD,
        EBreedingItem.OUCHIES_ORNAMENT,
        EBreedingItem.LOCKED_IN_STATE,
      ]

      const allowedBothParentsItems = [
        EBreedingItem.SELENIUM,
        EBreedingItem.SELENIUM_PRO,
        EBreedingItem.COMMON_INCUBATOR,
        EBreedingItem.ARCANE_INCUBATOR,
      ]

      const invalidLeftParentItems = leftParentItems.filter(
        (item) => !allowedLeftRightParentItems.includes(item)
      )

      const invalidRightParentItems = rightParentItems.filter(
        (item) => !allowedLeftRightParentItems.includes(item)
      )

      const invalidBothParentsItems = bothParentsItems.filter(
        (item) => !allowedBothParentsItems.includes(item)
      )

      if (invalidLeftParentItems.length > 0) {
        response.status(400)
        return response.json({
          status: 0,
          message: `Invalid breeding item for left parent: ${invalidLeftParentItems.join(', ')}`,
        })
      }

      if (invalidRightParentItems.length > 0) {
        response.status(400)
        return response.json({
          status: 0,
          message: `Invalid breeding item for right parent: ${invalidRightParentItems.join(', ')}`,
        })
      }

      if (invalidBothParentsItems.length > 0) {
        response.status(400)
        return response.json({
          status: 0,
          message: `Invalid breeding item for both parents: ${invalidBothParentsItems.join(', ')}`,
        })
      }

      // Check parent ownership
      const chickenLeftTokenIdOwner =
        chickenLeftTokenId > SABONG_CONFIG.CHICKEN_GENESIS_THRESHOLD
          ? await chickenLegacyContract.read.ownerOf([BigInt(chickenLeftTokenId)])
          : await chickenGenesisContract.read.ownerOf([BigInt(chickenLeftTokenId)])

      if (address.toLowerCase() !== chickenLeftTokenIdOwner.toLowerCase()) {
        response.status(400)
        return response.json({
          status: 0,
          message: 'chickenLeftTokenId is not owned by the owner wallet address',
        })
      }

      const checkChickenLeftIsHatched = await Chicken.query()
        .where('tokenId', chickenLeftTokenId)
        .where('isHatched', EChickenIsHatchedStatus.YES)
        .first()

      if (!checkChickenLeftIsHatched) {
        response.status(400)
        return response.json({
          status: 0,
          message: 'chickenLeftTokenId is not yet hatched!',
        })
      }

      const chickenRightTokenIdOwner =
        chickenRightTokenId > SABONG_CONFIG.CHICKEN_GENESIS_THRESHOLD
          ? await chickenLegacyContract.read.ownerOf([BigInt(chickenRightTokenId)])
          : await chickenGenesisContract.read.ownerOf([BigInt(chickenRightTokenId)])

      if (address.toLowerCase() !== chickenRightTokenIdOwner.toLowerCase()) {
        response.status(400)
        return response.json({
          status: 0,
          message: 'chickenRightTokenId is not owned by the owner wallet address',
        })
      }

      const checkChickenRightIsHatched = await Chicken.query()
        .where('tokenId', chickenRightTokenId)
        .where('isHatched', EChickenIsHatchedStatus.YES)
        .first()

      if (!checkChickenRightIsHatched) {
        response.status(400)
        return response.json({
          status: 0,
          message: 'chickenRightTokenId is not yet hatched!',
        })
      }

      // Check breeding cooldown
      const parentBreedingCooldown = (
        await breedingContract.read.getChickenBreedTimeBatch([
          [BigInt(chickenLeftTokenId), BigInt(chickenRightTokenId)],
        ])
      ).map((item) => Number(item))

      const isChickenLeftTokenIdDoneCooldown =
        parentBreedingCooldown[0] * 1000 <= DateTime.now().toMillis()

      if (!isChickenLeftTokenIdDoneCooldown) {
        response.status(400)
        return response.json({
          status: 0,
          message: 'chickenLeftTokenId is not done with its breeding cooldown',
        })
      }

      const isChickenRightTokenIdDoneCooldown =
        parentBreedingCooldown[1] * 1000 <= DateTime.now().toMillis()

      if (!isChickenRightTokenIdDoneCooldown) {
        response.status(400)
        return response.json({
          status: 0,
          message: 'ChickenRightTokenId is not done with its breeding cooldown',
        })
      }

      const parentBreedingCount = (
        await breedingContract.read.getChickenBreedCountBatch([
          [BigInt(chickenLeftTokenId), BigInt(chickenRightTokenId)],
        ])
      ).map((item) => Number(item))

      const chickenLeftBreedingCount = parentBreedingCount[0]

      const chickenRightBreedingCount = parentBreedingCount[1]

      const getChickenLeftTokenIdCooldownDuration = await BreedingCooldown.query()
        .where(
          'count',
          chickenLeftBreedingCount >= SABONG_CONFIG.BREEDING_COOLDOWN_MAX_COUNT_DURATION
            ? SABONG_CONFIG.BREEDING_COOLDOWN_MAX_COUNT_DURATION
            : chickenLeftBreedingCount
        )
        .first()

      if (!getChickenLeftTokenIdCooldownDuration) {
        response.status(400)
        return response.json({
          status: 0,
          message: 'chickenLeftTokenId does not have a valid breeding cooldown duration',
        })
      }

      const getChickenRightTokenIdCooldownDuration = await BreedingCooldown.query()
        .where(
          'count',
          chickenRightBreedingCount >= SABONG_CONFIG.BREEDING_COOLDOWN_MAX_COUNT_DURATION
            ? SABONG_CONFIG.BREEDING_COOLDOWN_MAX_COUNT_DURATION
            : chickenRightBreedingCount
        )
        .first()

      if (!getChickenRightTokenIdCooldownDuration) {
        response.status(400)
        return response.json({
          status: 0,
          message: 'chickenRightTokenId does not have a valid breeding cooldown duration',
        })
      }

      const chickenLeftTokenIdCooldownDuration = recalculateBreedingCooldown(
        chickenLeftBreedingCount,
        getChickenLeftTokenIdCooldownDuration
      )

      const chickenRightTokenIdCooldownDuration = recalculateBreedingCooldown(
        chickenRightBreedingCount,
        getChickenRightTokenIdCooldownDuration
      )

      const totalBreedingCooldown =
        chickenLeftTokenIdCooldownDuration.cooldown + chickenRightTokenIdCooldownDuration.cooldown

      // calculate breeding fee
      const getChickenLeftTokenIdBreedingFee = await BreedingFee.query()
        .where(
          'count',
          chickenLeftBreedingCount >= SABONG_CONFIG.BREEDING_COOLDOWN_MAX_COUNT_FEE
            ? SABONG_CONFIG.BREEDING_COOLDOWN_MAX_COUNT_FEE
            : chickenLeftBreedingCount
        )
        .first()

      if (!getChickenLeftTokenIdBreedingFee) {
        response.status(400)
        return response.json({
          status: 0,
          message: 'chickenLeftTokenId does not have a valid breeding fee',
        })
      }

      const getChickenRightTokenIdBreedingFee = await BreedingFee.query()
        .where(
          'count',
          chickenRightBreedingCount >= SABONG_CONFIG.BREEDING_COOLDOWN_MAX_COUNT_FEE
            ? SABONG_CONFIG.BREEDING_COOLDOWN_MAX_COUNT_FEE
            : chickenRightBreedingCount
        )
        .first()

      if (!getChickenRightTokenIdBreedingFee) {
        response.status(400)
        return response.json({
          status: 0,
          message: 'chickenRightTokenId does not have a valid breeding fee',
        })
      }

      const chickenLeftTokenIdBreedingFee = recalculateBreedingFee(
        chickenLeftBreedingCount,
        getChickenLeftTokenIdBreedingFee
      )

      const chickenRightTokenIdBreedingFee = recalculateBreedingFee(
        chickenRightBreedingCount,
        getChickenRightTokenIdBreedingFee
      )

      const breedingCockFee =
        chickenLeftTokenIdBreedingFee.cock + chickenRightTokenIdBreedingFee.cock
      const breedingFeatherFee =
        chickenLeftTokenIdBreedingFee.feathers + chickenRightTokenIdBreedingFee.feathers

      const totalAmount = breedingCockFee
      const amountToVault =
        ((100 - SABONG_CONFIG.BREEDING_NINUNO_PERCENTAGE_PER_PARENT * 2) / 100) * breedingCockFee

      const amountToNinuno =
        (SABONG_CONFIG.BREEDING_NINUNO_PERCENTAGE_PER_PARENT / 100) * breedingCockFee * 2

      const feathers = [[1, breedingFeatherFee]]
      let breedingCooldownTime = totalBreedingCooldown * 24 * 60 * 60 // days to seconds

      // Apply Selenium and Selenium Pro if exists
      if (bothParentsItems.includes(EBreedingItem.SELENIUM)) {
        breedingCooldownTime *= 0.75
      }

      if (bothParentsItems.includes(EBreedingItem.SELENIUM_PRO)) {
        breedingCooldownTime = 0
      }

      const charHash =
        keccak256(
          encodePacked(
            [
              'address', //sender
              'uint256', //chickenLeftTokenId
              'uint256', //chickenRightTokenId
              'uint256', //totalAmount
              'uint256', //amountToVault
              'uint256', //amountToNinuno
              'uint256', //breedingTime
              'bytes32', //feathersHash
              'bytes32', //resourcesHash
            ],
            [
              address as `0x${string}`,
              BigInt(chickenLeftTokenId),
              BigInt(chickenRightTokenId),
              parseEther(`${totalAmount}`),
              parseEther(`${amountToVault}`),
              parseEther(`${amountToNinuno}`),
              BigInt(breedingCooldownTime),
              keccak256(
                encodeAbiParameters(
                  [{ type: 'uint256[][]' }],
                  [feathers.map((arr) => arr.map((n) => BigInt(n)))]
                )
              ),
              keccak256(
                encodeAbiParameters(
                  [{ type: 'uint256[][]' }],
                  [resources ? resources.map((arr) => arr.map((n) => BigInt(n))) : []]
                )
              ),
            ]
          )
        ) || ''

      const signature = await signMessage({
        message: { raw: charHash },
        privateKey: SABONG_CONFIG.SIGNER_KEY,
      })

      chickenLeftTokenIds.push(chickenLeftTokenId)
      chickenRightTokenIds.push(chickenRightTokenId)
      totalAmounts.push(totalAmount)
      amountsToVault.push(amountToVault)
      amountsToNinuno.push(amountToNinuno)
      breedingCooldownTimes.push(breedingCooldownTime)
      feathersData.push(feathers)
      resourcesData.push(resources ? resources : [])
      signatures.push(signature)
    }

    // Check Cock Balance
    const checkCockBalanceRaw = await cockContract.read.balanceOf([address as `0x${string}`])
    const checkCockDecimals = await cockContract.read.decimals()
    const checkCockBalance = Number(formatUnits(checkCockBalanceRaw, checkCockDecimals))

    if (totalAmounts.reduce((a, b) => a + b, 0) > checkCockBalance) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Insufficient $COCK balance',
      })
    }

    // Check Feather Balance

    const checkFeatherBalance = Number(
      await itemContract.read.balanceOf([address as `0x${string}`, BigInt(1)])
    )

    if (feathersData.reduce((a, b) => a + b[0][1], 0) > checkFeatherBalance) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Insufficient FEATHER balance',
      })
    }

    if (resourcesData && resourcesData.length > 0) {
      // Flatten all resources and combine amounts for same resource IDs
      const flattenedResources = resourcesData.flat()
      const resourceMap = new Map<number, number>()

      flattenedResources.forEach((resource) => {
        const resourceId = resource[1]
        const amount = resource[2]
        resourceMap.set(resourceId, (resourceMap.get(resourceId) || 0) + amount)
      })

      const uniqueResourceIds = Array.from(resourceMap.keys())
      const totalAmounts = Array.from(resourceMap.values())

      const checkResourcesBalance = await resourcesContract.read.balanceOfBatch([
        Array(uniqueResourceIds.length).fill(address as `0x${string}`),
        uniqueResourceIds.map((id) => BigInt(id)),
      ])

      const checkResourcesBalanceFormatted = checkResourcesBalance.map((item) =>
        Number(formatUnits(item, 0))
      )

      const insufficientResources = checkResourcesBalanceFormatted.filter(
        (balance, index) => balance < totalAmounts[index]
      )

      if (insufficientResources.length > 0) {
        response.status(400)
        return response.json({
          status: 0,
          message: `Insufficient resources balance: ${insufficientResources
            .map(
              (balance, index) => `${uniqueResourceIds[index]}: ${balance}/${totalAmounts[index]}`
            )
            .join(', ')}`,
        })
      }
    }

    return response.json({
      status: 1,
      data: {
        address,
        chickenLeftTokenIds: chickenLeftTokenIds.map((item) => BigInt(item)),
        chickenRightTokenIds: chickenRightTokenIds.map((item) => BigInt(item)),
        totalAmounts: totalAmounts.map((item) => parseEther(`${item}`)),
        amountsToVault: amountsToVault.map((item) => parseEther(`${item}`)),
        amountsToNinuno: amountsToNinuno.map((item) => parseEther(`${item}`)),
        breedingCooldownTimes: breedingCooldownTimes.map((item) => BigInt(item)),
        feathersData: feathersData.map((arr) => arr.map((n) => n.map((o) => BigInt(o)))),
        resourcesData: resourcesData.map((arr) => arr.map((n) => n.map((o) => BigInt(o)))),
        signatures,
      },
    })
  }

  public async hatch({ request, response }: HttpContextContract) {
    const { chickenTokenId } = request.body()
    try {
      const findChicken = await Chicken.findBy('tokenId', chickenTokenId)

      if (!findChicken) {
        response.status(400)
        return response.json({
          status: 0,
          message: 'chickenTokenId not found',
        })
      }

      if (findChicken.isHatched === EChickenIsHatchedStatus.YES) {
        response.status(400)
        return response.json({
          status: 0,
          message: 'chickenTokenId is already hatched',
        })
      }

      if (findChicken.hatchedAt && findChicken.hatchedAt.toMillis() > DateTime.now().toMillis()) {
        response.status(400)
        return response.json({
          status: 0,
          message: 'chickenTokenId is not yet ready to be hatched',
        })
      }

      if (findChicken.tokenId <= 12487) {
        rerollInnatePoints(findChicken.tokenId)
      }

      await syncMetadata(findChicken.tokenId, true)

      findChicken.isHatched = EChickenIsHatchedStatus.YES
      await findChicken.save()

      return response.json({
        status: 1,
        message: 'Hatched successfully',
      })
    } catch (error) {
      response.status(400)
      return response.json({
        status: 0,
        message: `Failed to hatch chicken: ${error.message}`,
      })
    }
  }

  public async viewBreedingHistory({ request, response }: HttpContextContract) {
    const { tokenId } = request.qs()

    const findChicken = await Chicken.findBy('tokenId', tokenId)

    if (!findChicken) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'chickenTokenId not found',
      })
    }

    const breedingHistory = await BreedEvent.query()
      .where((subQuery) => {
        subQuery
          .whereJsonSuperset('args', { chickenLeftTokenId: tokenId })
          .orWhereJsonSuperset('args', { chickenRightTokenId: tokenId })
      })
      .orderBy('id', 'desc')

    return response.json({
      status: 1,
      data: breedingHistory,
    })
  }

  public async resync({ request, response }: HttpContextContract) {
    const { chickenTokenId, apiKey } = request.body()

    if (apiKey !== SABONG_CONFIG.SKYMAVIS_API_KEY) {
      response.status(401)
      return response.json({
        status: 0,
        message: 'Unauthorized',
      })
    }

    try {
      const findChicken = await Chicken.findBy('tokenId', chickenTokenId)

      if (!findChicken) {
        response.status(400)
        return response.json({
          status: 0,
          message: 'chickenTokenId not found',
        })
      }

      if (findChicken.isHatched === EChickenIsHatchedStatus.YES) {
        response.status(400)
        return response.json({
          status: 0,
          message: 'chickenTokenId is already hatched',
        })
      }

      const createEvent = await BreedEvent.query()
        .whereJsonSuperset('args', { newTokenId: chickenTokenId })
        .first()

      if (!createEvent) {
        response.status(400)
        return response.json({
          status: 0,
          message: 'Failed to find breeding event',
        })
      }

      const baseUri = `${SABONG_CONFIG.CHICKEN_IVORY_API_URL}/api/image`
      const buildDir = `${Application.tmpPath()}/build`

      let eggMetadata = {
        name: 'Egg',
        description: '',
        image: `${baseUri}/${chickenTokenId}.png`,
        attributes: [
          {
            trait_type: 'Type',
            value: 'Egg',
            display_type: 'string',
          },
          {
            trait_type: 'Parent 1',
            value: Number(findChicken.chickenLeftTokenId),
            display_type: 'number',
          },
          {
            trait_type: 'Parent 2',
            value: Number(findChicken.chickenRightTokenId),
            display_type: 'number',
          },
        ],
      }

      fs.writeFileSync(`${buildDir}/json/egg.json`, JSON.stringify(eggMetadata, null, 2))

      await syncMetadata(findChicken.tokenId, false)

      if (findChicken.tokenId !== 12956) {
        await incrementParentBreedCount(
          Number(createEvent.args.chickenLeftTokenId),
          Number(createEvent.args.breedingCooldownTime)
        )
        await incrementParentBreedCount(
          Number(createEvent.args.chickenRightTokenId),
          Number(createEvent.args.breedingCooldownTime)
        )
      }

      return response.json({
        status: 1,
        message: 'Resync successfully',
      })
    } catch (error) {
      response.status(400)
      return response.json({
        status: 0,
        message: `Failed to resync chicken: ${error.message}`,
      })
    }
  }
}
