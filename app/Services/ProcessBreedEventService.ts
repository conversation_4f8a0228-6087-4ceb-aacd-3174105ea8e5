/* eslint-disable @typescript-eslint/naming-convention */
import Bull from '@ioc:Rocketseat/Bull'
import { decodeGene } from 'App/Helper/chicken'
import ProcessBreedEventJob from 'App/Jobs/ProcessBreedEventJob'
import BreedEvent from 'App/Models/BreedEvent'
import BreedEventDistribution from 'App/Models/BreedEventDistribution'
import Chicken, { EChickenIsHatchedStatus } from 'App/Models/Chicken'
import SABONG_CONFIG, { EBreedingItem } from 'Config/sabong'
import { DateTime } from 'luxon'
import { breed, Traits } from './../../feature/hashlips'
import axios from 'axios'

export type BreedEventType = {
  address: `0x${string}`
  blockHash: `0x${string}`
  blockNumber: bigint
  data: `0x${string}`
  logIndex: number
  transactionHash: `0x${string}`
  transactionIndex: number
  removed: boolean
} & {
  args: {
    chickenLeftTokenId?: bigint | undefined
    chickenRightTokenId?: bigint | undefined
    newTokenId?: bigint | undefined
    amountToNinuno?: bigint | undefined
    feathersData?: readonly (readonly bigint[])[] | undefined
    resourcesData?: readonly (readonly bigint[])[] | undefined
    breedingCooldownTime?: bigint | undefined
  }
  eventName: string
}

const distributeAmountToNinuno = async (
  ninuno: number[],
  amountToNinuno: bigint,
  transactionHash: string
) => {
  const distributionWeight = SABONG_CONFIG.BREEDING_CONFIG.NINUNO_DISTRIBUTION_WEIGHTS

  let totalWeight = 0
  let totalDistributed = 0n

  for (const ninunoTokenId of ninuno) {
    const findChicken = await Chicken.findBy('tokenId', ninunoTokenId)

    if (!findChicken) {
      throw new Error(`ninunoTokenId ${ninunoTokenId} not found`)
    }

    // check ninunos legendary count
    const decodedGene = decodeGene(findChicken.genes)
    const legendaryCount = Number(decodedGene?.LegendaryCount) || 0

    if (legendaryCount >= 3) {
      totalWeight += 10
    } else if (legendaryCount === 2) {
      totalWeight += 5
    } else if (legendaryCount === 1) {
      totalWeight += 3
    } else if (ninunoTokenId <= SABONG_CONFIG.CHICKEN_GENESIS_THRESHOLD) {
      totalWeight += 2
    } else {
      totalWeight += 1
    }
  }

  for (const ninunoTokenId of ninuno) {
    const findChicken = await Chicken.findBy('tokenId', ninunoTokenId)

    if (!findChicken) {
      throw new Error(`ninunoTokenId ${ninunoTokenId} not found`)
    }

    let amount = 0n

    // check ninunos legendary count
    const decodedGene = decodeGene(findChicken.genes)
    const legendaryCount = Number(decodedGene?.LegendaryCount) || 0

    if (legendaryCount >= 3) {
      amount = (amountToNinuno * BigInt(distributionWeight[0])) / BigInt(totalWeight)

      totalDistributed += amount
      findChicken.balance += amount
    } else if (legendaryCount === 2) {
      amount = (amountToNinuno * BigInt(distributionWeight[1])) / BigInt(totalWeight)

      totalDistributed += amount
      findChicken.balance += amount
    } else if (legendaryCount === 1) {
      amount = (amountToNinuno * BigInt(distributionWeight[2])) / BigInt(totalWeight)

      totalDistributed += amount
      findChicken.balance += amount
    } else if (ninunoTokenId <= SABONG_CONFIG.CHICKEN_GENESIS_THRESHOLD) {
      amount = (amountToNinuno * BigInt(distributionWeight[3])) / BigInt(totalWeight)

      totalDistributed += amount
      findChicken.balance += amount
    } else {
      amount = (amountToNinuno * BigInt(distributionWeight[4])) / BigInt(totalWeight)

      totalDistributed += amount
      findChicken.balance += amount
    }

    await findChicken.save()

    await BreedEventDistribution.create({
      tokenId: Number(ninunoTokenId),
      transactionHash: transactionHash,
      amount: amount,
    })
  }

  return totalDistributed
}

export const incrementParentBreedCount = async (tokenId: number, timestamp: number) => {
  try {
    await axios.post(
      `${SABONG_CONFIG.CHICKEN_IVORY_API_URL}/api/metadata/breed-count/${tokenId}`,
      {
        timestamp: timestamp,
      },
      {
        headers: {
          'x-api-key': SABONG_CONFIG.CHICKEN_IVORY_API_KEY,
        },
      }
    )
  } catch (error) {
    console.error(`Failed to sync breed-count of ${tokenId}:`, error)
  } finally {
    refreshMetadata(
      [tokenId.toString()],
      SABONG_CONFIG.CHICKEN_GENESIS_THRESHOLD >= tokenId
        ? SABONG_CONFIG.CONTRACTS.CHICKEN_GENESIS_ADDRESS
        : SABONG_CONFIG.CONTRACTS.CHICKEN_LEGACY_ADDRESS
    )
  }
}

const refreshMetadata = async (tokenIds: string[], collection: string) => {
  try {
    const response = await axios.post(
      `https://api-gateway.skymavis.com/mavis-market-partner/collections/${collection}/refresh_metadata`,
      {
        token_ids: tokenIds,
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': SABONG_CONFIG.SKYMAVIS_API_KEY,
        },
      }
    )

    return response.data
  } catch (error) {
    console.error(`Failed to refresh metadata ${tokenIds}:`, error)
  }
}

export const processData = async (breedEvent: BreedEvent) => {
  // TODO: start database transaction and revert upon fail
  try {
    const findChickenLeft = await Chicken.findBy(
      'tokenId',
      Number(breedEvent.args.chickenLeftTokenId)
    )

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy(
      'tokenId',
      Number(breedEvent.args.chickenRightTokenId)
    )

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    const createChicken = await Chicken.updateOrCreate(
      {
        id: Number(breedEvent.args.newTokenId),
        tokenId: Number(breedEvent.args.newTokenId),
      },
      {
        id: Number(breedEvent.args.newTokenId),
        tokenId: Number(breedEvent.args.newTokenId),
        chickenLeftTokenId: Number(breedEvent.args.chickenLeftTokenId),
        chickenRightTokenId: Number(breedEvent.args.chickenRightTokenId),
        balance: 0n,
        generation:
          findChickenLeft.generation > findChickenRight.generation
            ? findChickenLeft.generation + 1
            : findChickenRight.generation + 1,
      }
    )

    const chickenLeft = decodeGene(findChickenLeft.genes) as Traits
    const chickenRight = decodeGene(findChickenRight.genes) as Traits

    const leftParentItems: number[] =
      breedEvent.args.resourcesData
        ?.filter((item: any[]) => Number(item?.[0]) === 0)
        .map((item: any[]) => Number(item?.[1])) || []

    const bothParentsItems: number[] =
      breedEvent.args.resourcesData
        ?.filter((item: any[]) => Number(item?.[0]) === 1)
        .map((item: any[]) => Number(item?.[1])) || []

    const rightParentItems: number[] =
      breedEvent.args.resourcesData
        ?.filter((item: any[]) => Number(item?.[0]) === 2)
        .map((item: any[]) => Number(item?.[1])) || []

    const breedingItems = [leftParentItems, bothParentsItems, rightParentItems]

    const { genes } = await breed(
      chickenLeft,
      chickenRight,
      Number(breedEvent.args.newTokenId),
      Number(breedEvent.args.chickenLeftTokenId),
      Number(breedEvent.args.chickenRightTokenId),
      `Gen ${createChicken.generation}`,
      breedingItems
    )

    const combinedNinuno = [
      Number(breedEvent.args.chickenLeftTokenId),
      Number(breedEvent.args.chickenRightTokenId),
      ...findChickenLeft.ninuno,
      ...findChickenRight.ninuno,
    ]
      .filter((ninunoTokenId) => ninunoTokenId <= SABONG_CONFIG.CHICKEN_LEGACY_THRESHOLD) // remove ninuno that is not legacy
      .filter(
        (ninuno, index, self) => index === self.findIndex((t) => t === ninuno) // remove duplicate ninuno
      )

    createChicken.ninuno = combinedNinuno
    createChicken.genes = genes
    createChicken.isHatched = EChickenIsHatchedStatus.NO

    if (breedEvent.args.breedingCooldownTime) {
      let hatchTimeReduction = SABONG_CONFIG.BREEDING_HATCHING_DAYS

      if (bothParentsItems.includes(EBreedingItem.COMMON_INCUBATOR)) {
        // Common Incubator - 25% hatch time reduction
        hatchTimeReduction *= 0.75
      }

      if (bothParentsItems.includes(EBreedingItem.ARCANE_INCUBATOR)) {
        // Arcane Incubator - Instant hatch
        hatchTimeReduction = 0
      }

      createChicken.hatchedAt = DateTime.fromSeconds(
        Number(breedEvent.args.breedingCooldownTime)
      ).plus({
        seconds: hatchTimeReduction,
      })

      createChicken.hatchedTime = hatchTimeReduction
      createChicken.parentBreedingStateTime = Number(breedEvent.args.breedingCooldownTime)
    }

    await createChicken.save()

    const totalDistributed = await distributeAmountToNinuno(
      combinedNinuno,
      BigInt(breedEvent.args.amountToNinuno),
      breedEvent.transactionHash
    )

    return {
      isSuccess: true,
      totalDistributed,
      amountToNinuno: BigInt(breedEvent.args.amountToNinuno),
    }
  } catch (error) {
    console.log('Error processing breed event:', error)
    return {
      isSuccess: false,
    }
  }
}

export const processBreedEvent = async (breedEvents: BreedEventType[]) => {
  for (const breedEvent of breedEvents) {
    await Bull.add(
      new ProcessBreedEventJob().key,
      JSON.parse(
        JSON.stringify(breedEvent, (_, value) => {
          if (typeof value === 'bigint') {
            return value.toString()
          } else {
            return value
          }
        })
      ),
      {
        attempts: 1,
      }
    )
  }
}
