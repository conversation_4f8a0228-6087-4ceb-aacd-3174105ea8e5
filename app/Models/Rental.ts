import { DateTime } from 'luxon'
import { BaseModel, column, hasMany, Has<PERSON>any } from '@ioc:Adonis/Lucid/Orm'
import RentalHistoryEvent from './RentalHistoryEvent'

export enum RentalStatus {
  AVAILABLE = 0,
  RENTED = 1,
  EXPIRED = 2,
  CANCELLED = 3,
  PENDING = 4,
}

export enum RewardDistributionType {
  DELEGATOR_ONLY = 1,
  DELEGATEE_ONLY = 2,
  SHARED = 3,
}

export enum GameRewardDistributionType {
  DELEGATOR_ONLY = 1,
  DELEGATEE_ONLY = 2,
}

export enum DelegatedTaskType {
  DAILY_RUB = 1,
  GAMEPLAY = 2,
  BOTH = 3,
}

export enum RubStreakBenefactorType {
  DELEGATOR = 1,
  DELEGATEE = 2,
}

export enum LegendaryFeatherBenefactorType {
  DELEGATOR = 1,
  DELEGATEE = 2,
}

export default class Rental extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public chickenTokenId: number

  @column()
  public ownerAddress: string

  @column()
  public renterAddress: string | null

  @column({
    prepare: (value) => value.toString(),
    consume: (value: string) => {
      return value && typeof value === 'string' ? BigInt(value) : value
    },
  })
  public roninPrice: bigint

  @column({
    prepare: (value) => value.toString(),
    consume: (value: string) => {
      return value && typeof value === 'string' ? BigInt(value) : value
    },
  })
  public insurancePrice: bigint

  @column()
  public rentalPeriod: number

  @column.dateTime()
  public rentedAt: DateTime | null

  @column.dateTime()
  public expiresAt: DateTime | null

  @column()
  public status: RentalStatus

  @column()
  public signature: string | null

  @column()
  public rewardDistribution: RewardDistributionType

  @column()
  public gameRewardDistribution: GameRewardDistributionType

  @column()
  public delegatedTask: DelegatedTaskType

  @column()
  public rubStreakBenefactor: RubStreakBenefactorType

  @column()
  public legendaryFeatherBenefactor: LegendaryFeatherBenefactorType

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column()
  public sharedRewardAmount: number | null

  @column()
  public insuranceClaimed: boolean

  @hasMany(() => RentalHistoryEvent)
  public historyEvents: HasMany<typeof RentalHistoryEvent>
}
