import { test } from '@japa/runner'
import { breed, Traits } from '../../feature/hashlips'
import { decodeGene } from 'App/Helper/chicken'
import Chicken from 'App/Models/Chicken'
import { EBreedingItem } from 'Config/sabong'

test.group('Test Soulknot', () => {
  let parentLeft: Traits
  let parentRight: Traits

  test('Left parent has Soulknot', async () => {
    let soulknotTriggered = false

    // Run multiple times to test probability
    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10001 + i,
        1001,
        1002,
        'Gen 1',
        [[EBreedingItem.SOULKNOT], []] // Soulknot on left parent
      )

      // Check if any IP values match exactly with left parent (indicating Soulknot effect)
      const leftIPs = [
        parentLeft['Innate Attack'],
        parentLeft['Innate Defense'],
        parentLeft['Innate Speed'],
        parentLeft['Innate Health'],
      ]
      const offspringIPs = [
        traits['Innate Attack'],
        traits['Innate Defense'],
        traits['Innate Speed'],
        traits['Innate Health'],
      ]

      const exactMatches = offspringIPs.filter((ip) => leftIPs.includes(ip)).length
      if (exactMatches >= 3) {
        soulknotTriggered = true
        break
      }
    }

    console.log('Soulknot effect triggered:', soulknotTriggered)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })

  test('Right parent has Soulknot', async () => {
    let soulknotTriggered = false

    // Run multiple times to test probability
    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10001 + i,
        1001,
        1002,
        'Gen 1',
        [[], [], [EBreedingItem.SOULKNOT]] // Soulknot on right parent
      )

      // Check if any IP values match exactly with left parent (indicating Soulknot effect)
      const rightIPs = [
        parentRight['Innate Attack'],
        parentRight['Innate Defense'],
        parentRight['Innate Speed'],
        parentRight['Innate Health'],
      ]
      const offspringIPs = [
        traits['Innate Attack'],
        traits['Innate Defense'],
        traits['Innate Speed'],
        traits['Innate Health'],
      ]

      const exactMatches = offspringIPs.filter((ip) => rightIPs.includes(ip)).length
      if (exactMatches >= 3) {
        soulknotTriggered = true
        break
      }
    }

    console.log('Soulknot effect triggered:', soulknotTriggered)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })

  test('Both parent has Soulknot', async () => {
    let soulknotTriggered = false

    // Run multiple times to test probability
    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10001 + i,
        1001,
        1002,
        'Gen 1',
        [[EBreedingItem.SOULKNOT], [], [EBreedingItem.SOULKNOT]] // Soulknot on right parent
      )

      // Check if any IP values match exactly with left parent (indicating Soulknot effect)
      const rightIPs = [
        parentRight['Innate Attack'],
        parentRight['Innate Defense'],
        parentRight['Innate Speed'],
        parentRight['Innate Health'],
      ]
      const leftIPs = [
        parentLeft['Innate Attack'],
        parentLeft['Innate Defense'],
        parentLeft['Innate Speed'],
        parentLeft['Innate Health'],
      ]
      const offspringIPs = [
        traits['Innate Attack'],
        traits['Innate Defense'],
        traits['Innate Speed'],
        traits['Innate Health'],
      ]

      const exactMatches = offspringIPs.filter(
        (ip) => rightIPs.includes(ip) || leftIPs.includes(ip)
      ).length
      if (exactMatches >= 3) {
        soulknotTriggered = true
        break
      }
    }

    console.log('Soulknot effect triggered:', soulknotTriggered)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })
})

test.group('Test Randomizer', () => {
  let parentLeft: Traits
  let parentRight: Traits

  test('Left parent has Randomizer', async ({ assert }) => {
    const { traits } = await breed(
      parentLeft,
      parentRight,
      10002,
      1001,
      1002,
      'Gen 1',
      [[EBreedingItem.RANDOMIZER], []] // Randomizer on left parent
    )

    // With randomizer, traits should not necessarily match parent traits
    assert.exists(traits.Feet)
    assert.exists(traits.Tail)
    assert.exists(traits.Body)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })

  test('Right parent has Randomizer', async ({ assert }) => {
    const { traits } = await breed(
      parentLeft,
      parentRight,
      10002,
      1001,
      1002,
      'Gen 1',
      [[], [], [EBreedingItem.RANDOMIZER]] // Randomizer on right parent
    )

    // With randomizer, traits should not necessarily match parent traits
    assert.exists(traits.Feet)
    assert.exists(traits.Tail)
    assert.exists(traits.Body)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })

  test('Both parent has Randomizer', async ({ assert }) => {
    const { traits } = await breed(
      parentLeft,
      parentRight,
      10002,
      1001,
      1002,
      'Gen 1',
      [[EBreedingItem.RANDOMIZER], [], [EBreedingItem.RANDOMIZER]] // Randomizer on right parent
    )

    // With randomizer, traits should not necessarily match parent traits
    assert.exists(traits.Feet)
    assert.exists(traits.Tail)
    assert.exists(traits.Body)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })
})

test.group('Test Gregors Gift', () => {
  let parentLeft: Traits
  let parentRight: Traits

  test('Left parent has gregors gift', async ({ assert }) => {
    // This would affect H1, H2, H3 gene selection probability
    const { traits } = await breed(
      parentLeft,
      parentRight,
      10004,
      1001,
      1002,
      'Gen 1',
      [[EBreedingItem.GREGORS_GIFT], [], []] // Gregor's Gift on left parent
    )

    console.log("Gregor's Gift offspring:", traits)
    console.table([traits, parentLeft, parentRight])
    assert.exists(traits)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })

  test('Right parent has gregors gift', async ({ assert }) => {
    // This would affect H1, H2, H3 gene selection probability
    const { traits } = await breed(
      parentLeft,
      parentRight,
      10004,
      1001,
      1002,
      'Gen 1',
      [[], [], [EBreedingItem.GREGORS_GIFT]] // Gregor's Gift on right parent
    )

    console.log("Gregor's Gift offspring:", traits)
    console.table([traits, parentLeft, parentRight])
    assert.exists(traits)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })

  test('Both parent has gregors gift', async ({ assert }) => {
    // This would affect H1, H2, H3 gene selection probability
    const { traits } = await breed(
      parentLeft,
      parentRight,
      10004,
      1001,
      1002,
      'Gen 1',
      [[EBreedingItem.GREGORS_GIFT], [], [EBreedingItem.GREGORS_GIFT]] // Gregor's Gift on both parent
    )

    console.log("Gregor's Gift offspring:", traits)
    console.table([traits, parentLeft, parentRight])
    assert.exists(traits)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })
})

test.group('Test Mendels Memento', () => {
  let parentLeft: Traits
  let parentRight: Traits

  test('Left parent has mendels memento', async ({ assert }) => {
    // This would affect P gene selection probability
    const { traits } = await breed(
      parentLeft,
      parentRight,
      10005,
      1001,
      1002,
      'Gen 1',
      [[EBreedingItem.MENDELS_MEMENTO], [], []] // Mendel's Memento on left parent
    )

    assert.exists(traits)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })

  test('Right parent has mendels memento', async ({ assert }) => {
    // This would affect P gene selection probability
    const { traits } = await breed(
      parentLeft,
      parentRight,
      10005,
      1001,
      1002,
      'Gen 1',
      [[], [], [EBreedingItem.MENDELS_MEMENTO]] // Mendel's Memento on right parent
    )

    assert.exists(traits)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })

  test('Both parent has mendels memento', async ({ assert }) => {
    // This would affect P gene selection probability
    const { traits } = await breed(
      parentLeft,
      parentRight,
      10005,
      1001,
      1002,
      'Gen 1',
      [[EBreedingItem.MENDELS_MEMENTO], [], [EBreedingItem.MENDELS_MEMENTO]] // Mendel's Memento on right parent
    )

    assert.exists(traits)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })
})

test.group('Test Quentins Talon', () => {
  let parentLeft: Traits
  let parentRight: Traits

  test('Left parent has quentins talon', async ({ assert }) => {
    let leftFeetInherited = 0

    // Run multiple times to test probability
    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10006 + i,
        1001,
        1002,
        'Gen 1',
        [[EBreedingItem.QUENTINS_TALON], [], []] // Quentin's Talon on left parent
      )

      // Check if offspring inherited left parent's feet traits
      const leftFeetTraits = [
        parentLeft.Feet.p,
        parentLeft.Feet.h1,
        parentLeft.Feet.h2,
        parentLeft.Feet.h3,
      ]
      if (leftFeetTraits.includes(traits.Feet.p)) {
        leftFeetInherited++
      }
    }

    console.log(`Left parent feet inherited: ${leftFeetInherited}/20 times`)
    assert.isAbove(leftFeetInherited, 8)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })

  test('Right parent has quentins talon', async ({ assert }) => {
    let rightFeetInherited = 0

    // Run multiple times to test probability
    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10006 + i,
        1001,
        1002,
        'Gen 1',
        [[], [], [EBreedingItem.QUENTINS_TALON]] // Quentin's Talon on right parent
      )

      // Check if offspring inherited left parent's feet traits
      const rightFeetTraits = [
        parentRight.Feet.p,
        parentRight.Feet.h1,
        parentRight.Feet.h2,
        parentRight.Feet.h3,
      ]
      if (rightFeetTraits.includes(traits.Feet.p)) {
        rightFeetInherited++
      }
    }

    console.log(`Right parent feet inherited: ${rightFeetInherited}/20 times`)
    assert.isAbove(rightFeetInherited, 8)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })

  test('Both parent has quentins talon', async ({ assert }) => {
    let leftFeetInherited = 0

    // Run multiple times to test probability
    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10006 + i,
        1001,
        1002,
        'Gen 1',
        [[EBreedingItem.QUENTINS_TALON], [], [EBreedingItem.QUENTINS_TALON]] // Quentin's Talon on both parent
      )

      // Check if offspring inherited left parent's feet traits
      const leftFeetTraits = [
        parentLeft.Feet.p,
        parentLeft.Feet.h1,
        parentLeft.Feet.h2,
        parentLeft.Feet.h3,
      ]

      const rightFeetTraits = [
        parentRight.Feet.p,
        parentRight.Feet.h1,
        parentRight.Feet.h2,
        parentRight.Feet.h3,
      ]

      if ([...leftFeetTraits, ...rightFeetTraits].includes(traits.Feet.p)) {
        leftFeetInherited++
      }
    }

    console.log(`Either parent feet inherited: ${leftFeetInherited}/20 times`)
    assert.isAbove(leftFeetInherited, 8)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })
})

test.group('Test Dragons Whip', () => {
  let parentLeft: Traits
  let parentRight: Traits

  test('Left parent has dragons whip', async ({ assert }) => {
    let leftTailInherited = 0

    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10007 + i,
        1001,
        1002,
        'Gen 1',
        [[EBreedingItem.DRAGONS_WHIP], [], []] // Dragon's Whip on left parent
      )

      const leftTailTraits = [
        parentLeft.Tail.p,
        parentLeft.Tail.h1,
        parentLeft.Tail.h2,
        parentLeft.Tail.h3,
      ]
      if (leftTailTraits.includes(traits.Tail.p)) {
        leftTailInherited++
      }
    }

    console.log(`Left parent tail inherited: ${leftTailInherited}/20 times`)
    assert.isAbove(leftTailInherited, 8)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })

  test('Right parent has dragons whip', async ({ assert }) => {
    let rightTailInherited = 0

    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10007 + i,
        1001,
        1002,
        'Gen 1',
        [[], [], [EBreedingItem.DRAGONS_WHIP]] // Dragon's Whip on right parent
      )

      const rightTailTraits = [
        parentRight.Tail.p,
        parentRight.Tail.h1,
        parentRight.Tail.h2,
        parentRight.Tail.h3,
      ]
      if (rightTailTraits.includes(traits.Tail.p)) {
        rightTailInherited++
      }
    }

    console.log(`Right parent tail inherited: ${rightTailInherited}/20 times`)
    assert.isAbove(rightTailInherited, 8)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })

  test('Both parent has dragons whip', async ({ assert }) => {
    let leftTailInherited = 0

    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10007 + i,
        1001,
        1002,
        'Gen 1',
        [[EBreedingItem.DRAGONS_WHIP], [], [EBreedingItem.DRAGONS_WHIP]] // Dragon's Whip on right parent
      )

      const leftTailTraits = [
        parentLeft.Tail.p,
        parentLeft.Tail.h1,
        parentLeft.Tail.h2,
        parentLeft.Tail.h3,
      ]

      const rightTailTraits = [
        parentRight.Tail.p,
        parentRight.Tail.h1,
        parentRight.Tail.h2,
        parentRight.Tail.h3,
      ]

      if ([...leftTailTraits, ...rightTailTraits].includes(traits.Tail.p)) {
        leftTailInherited++
      }
    }

    console.log(`Either parent tail inherited: ${leftTailInherited}/20 times`)
    assert.isAbove(leftTailInherited, 8)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })
})

test.group("Test Chibidei's Curse", () => {
  let parentLeft: Traits
  let parentRight: Traits

  test("Left parent has chibidei's curse", async ({ assert }) => {
    let leftBodyInherited = 0

    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10008 + i,
        1001,
        1002,
        'Gen 1',
        [[EBreedingItem.CHIBIDEIS_CURSE], [], []] // Chibidei's Curse on left parent
      )

      const leftBodyTraits = [
        parentLeft.Body.p,
        parentLeft.Body.h1,
        parentLeft.Body.h2,
        parentLeft.Body.h3,
      ]
      if (leftBodyTraits.includes(traits.Body.p)) {
        leftBodyInherited++
      }
    }

    console.log(`Left parent body inherited: ${leftBodyInherited}/20 times`)
    assert.isAbove(leftBodyInherited, 8)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })

  test("Right parent has chibidei's curse", async ({ assert }) => {
    let rightBodyInherited = 0

    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10008 + i,
        1001,
        1002,
        'Gen 1',
        [[], [], [EBreedingItem.CHIBIDEIS_CURSE]] // Chibidei's Curse on right parent
      )

      const rightBodyTraits = [
        parentRight.Body.p,
        parentRight.Body.h1,
        parentRight.Body.h2,
        parentRight.Body.h3,
      ]
      if (rightBodyTraits.includes(traits.Body.p)) {
        rightBodyInherited++
      }
    }

    console.log(`Right parent body inherited: ${rightBodyInherited}/20 times`)
    assert.isAbove(rightBodyInherited, 8)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })

  test("Both parent has chibidei's curse", async ({ assert }) => {
    let leftBodyInherited = 0

    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10008 + i,
        1001,
        1002,
        'Gen 1',
        [[EBreedingItem.CHIBIDEIS_CURSE], [], [EBreedingItem.CHIBIDEIS_CURSE]] // Chibidei's Curse on both parent
      )

      const leftBodyTraits = [
        parentLeft.Body.p,
        parentLeft.Body.h1,
        parentLeft.Body.h2,
        parentLeft.Body.h3,
      ]

      const rightBodyTraits = [
        parentRight.Body.p,
        parentRight.Body.h1,
        parentRight.Body.h2,
        parentRight.Body.h3,
      ]

      if ([...leftBodyTraits, ...rightBodyTraits].includes(traits.Body.p)) {
        leftBodyInherited++
      }
    }

    console.log(`Either parent body inherited: ${leftBodyInherited}/20 times`)
    assert.isAbove(leftBodyInherited, 8)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })
})

test.group('Test All Seeing Seed', () => {
  let parentLeft: Traits
  let parentRight: Traits

  test('Left parent has all seeing seed', async ({ assert }) => {
    let leftEyesInherited = 0

    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10009 + i,
        1001,
        1002,
        'Gen 1',
        [[EBreedingItem.ALL_SEEING_SEED], [], []] // All-seeing Seed on left parent
      )

      const leftEyesTraits = [
        parentLeft.Eyes.p,
        parentLeft.Eyes.h1,
        parentLeft.Eyes.h2,
        parentLeft.Eyes.h3,
      ]
      if (leftEyesTraits.includes(traits.Eyes.p)) {
        leftEyesInherited++
      }
    }

    console.log(`Left parent eyes inherited: ${leftEyesInherited}/20 times`)
    assert.isAbove(leftEyesInherited, 8)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })

  test('Right parent has all seeing seed', async ({ assert }) => {
    let rightEyesInherited = 0

    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10009 + i,
        1001,
        1002,
        'Gen 1',
        [[], [], [EBreedingItem.ALL_SEEING_SEED]] // All-seeing Seed on right parent
      )

      const rightEyesTraits = [
        parentRight.Eyes.p,
        parentRight.Eyes.h1,
        parentRight.Eyes.h2,
        parentRight.Eyes.h3,
      ]
      if (rightEyesTraits.includes(traits.Eyes.p)) {
        rightEyesInherited++
      }
    }

    console.log(`Right parent eyes inherited: ${rightEyesInherited}/20 times`)
    assert.isAbove(rightEyesInherited, 8)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })

  test('Both parent has all seeing seed', async ({ assert }) => {
    let leftEyesInherited = 0

    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10009 + i,
        1001,
        1002,
        'Gen 1',
        [[EBreedingItem.ALL_SEEING_SEED], [], [EBreedingItem.ALL_SEEING_SEED]] // All-seeing Seed on both parent
      )

      const leftEyesTraits = [
        parentLeft.Eyes.p,
        parentLeft.Eyes.h1,
        parentLeft.Eyes.h2,
        parentLeft.Eyes.h3,
      ]

      const rightEyesTraits = [
        parentRight.Eyes.p,
        parentRight.Eyes.h1,
        parentRight.Eyes.h2,
        parentRight.Eyes.h3,
      ]

      if ([...leftEyesTraits, ...rightEyesTraits].includes(traits.Eyes.p)) {
        leftEyesInherited++
      }
    }

    console.log(`Either parent eyes inherited: ${leftEyesInherited}/20 times`)
    assert.isAbove(leftEyesInherited, 8)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })
})

test.group("Test Chim Lac's Curio", () => {
  let parentLeft: Traits
  let parentRight: Traits

  test("Left parent has chim lac's curio", async ({ assert }) => {
    let leftBeakInherited = 0 // Beak is the trait that Chim Lac's Curio affects

    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10010 + i,
        1001,
        1002,
        'Gen 1',
        [[EBreedingItem.CHIM_LACS_CURIO], [], []] // Chim Lac's Curio on left parent
      )

      const leftBeakTraits = [
        parentLeft.Beak.p,
        parentLeft.Beak.h1,
        parentLeft.Beak.h2,
        parentLeft.Beak.h3,
      ]
      if (leftBeakTraits.includes(traits.Beak.p)) {
        leftBeakInherited++
      }
    }

    console.log(`Left parent beak inherited: ${leftBeakInherited}/20 times`)
    assert.isAbove(leftBeakInherited, 8)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })

  test("Right parent has chim lac's curio", async ({ assert }) => {
    let rightBeakInherited = 0 // Beak is the trait that Chim Lac's Curio affects

    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10010 + i,
        1001,
        1002,
        'Gen 1',
        [[], [], [EBreedingItem.CHIM_LACS_CURIO]] // Chim Lac's Curio on right parent
      )

      const rightBeakTraits = [
        parentRight.Beak.p,
        parentRight.Beak.h1,
        parentRight.Beak.h2,
        parentRight.Beak.h3,
      ]
      if (rightBeakTraits.includes(traits.Beak.p)) {
        rightBeakInherited++
      }
    }

    console.log(`Right parent beak inherited: ${rightBeakInherited}/20 times`)
    assert.isAbove(rightBeakInherited, 8)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })

  test("Both parent has chim lac's curio", async ({ assert }) => {
    let leftBeakInherited = 0 // Beak is the trait that Chim Lac's Curio affects

    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10010 + i,
        1001,
        1002,
        'Gen 1',
        [[EBreedingItem.CHIM_LACS_CURIO], [], [EBreedingItem.CHIM_LACS_CURIO]] // Chim Lac's Curio on both parent
      )

      const leftBeakTraits = [
        parentLeft.Beak.p,
        parentLeft.Beak.h1,
        parentLeft.Beak.h2,
        parentLeft.Beak.h3,
      ]

      const rightBeakTraits = [
        parentRight.Beak.p,
        parentRight.Beak.h1,
        parentRight.Beak.h2,
        parentRight.Beak.h3,
      ]

      if ([...leftBeakTraits, ...rightBeakTraits].includes(traits.Beak.p)) {
        leftBeakInherited++
      }
    }

    console.log(`Either parent beak inherited: ${leftBeakInherited}/20 times`)
    assert.isAbove(leftBeakInherited, 8)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })
})

test.group('Test Suave Scissors', () => {
  let parentLeft: Traits
  let parentRight: Traits

  test('Left parent has suave scissors', async ({ assert }) => {
    let leftCombInherited = 0

    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10011 + i,
        1001,
        1002,
        'Gen 1',
        [[EBreedingItem.SUAVE_SCISSORS], [], []] // Suave Scissors on left parent
      )

      const leftCombTraits = [
        parentLeft.Comb.p,
        parentLeft.Comb.h1,
        parentLeft.Comb.h2,
        parentLeft.Comb.h3,
      ]
      if (leftCombTraits.includes(traits.Comb.p)) {
        leftCombInherited++
      }
    }

    console.log(`Left parent comb inherited: ${leftCombInherited}/20 times`)
    assert.isAbove(leftCombInherited, 8)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })

  test('Right parent has suave scissors', async ({ assert }) => {
    let rightCombInherited = 0

    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10011 + i,
        1001,
        1002,
        'Gen 1',
        [[], [], [EBreedingItem.SUAVE_SCISSORS]] // Suave Scissors on right parent
      )

      const rightCombTraits = [
        parentRight.Comb.p,
        parentRight.Comb.h1,
        parentRight.Comb.h2,
        parentRight.Comb.h3,
      ]
      if (rightCombTraits.includes(traits.Comb.p)) {
        rightCombInherited++
      }
    }

    console.log(`Right parent comb inherited: ${rightCombInherited}/20 times`)
    assert.isAbove(rightCombInherited, 8)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })

  test('Both parent has suave scissors', async ({ assert }) => {
    let leftCombInherited = 0

    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10011 + i,
        1001,
        1002,
        'Gen 1',
        [[EBreedingItem.SUAVE_SCISSORS], [], [EBreedingItem.SUAVE_SCISSORS]] // Suave Scissors on both parent
      )

      const leftCombTraits = [
        parentLeft.Comb.p,
        parentLeft.Comb.h1,
        parentLeft.Comb.h2,
        parentLeft.Comb.h3,
      ]

      const rightCombTraits = [
        parentRight.Comb.p,
        parentRight.Comb.h1,
        parentRight.Comb.h2,
        parentRight.Comb.h3,
      ]

      if ([...leftCombTraits, ...rightCombTraits].includes(traits.Comb.p)) {
        leftCombInherited++
      }
    }

    console.log(`Either parent comb inherited: ${leftCombInherited}/20 times`)
    assert.isAbove(leftCombInherited, 8)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })
})

test.group("Test Simurgh's Sovereign", () => {
  let parentLeft: Traits
  let parentRight: Traits

  test("Left parent has simurgh's sovereign", async ({ assert }) => {
    let leftWingsInherited = 0

    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10012 + i,
        1001,
        1002,
        'Gen 1',
        [[EBreedingItem.SIMURGHS_SOVEREIGN], [], []] // Simurgh's Sovereign on left parent
      )

      const leftWingsTraits = [
        parentLeft.Wings.p,
        parentLeft.Wings.h1,
        parentLeft.Wings.h2,
        parentLeft.Wings.h3,
      ]
      if (leftWingsTraits.includes(traits.Wings.p)) {
        leftWingsInherited++
      }
    }

    console.log(`Left parent wings inherited: ${leftWingsInherited}/20 times`)
    assert.isAbove(leftWingsInherited, 8)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })

  test("Right parent has simurgh's sovereign", async ({ assert }) => {
    let rightWingsInherited = 0

    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10012 + i,
        1001,
        1002,
        'Gen 1',
        [[], [], [EBreedingItem.SIMURGHS_SOVEREIGN]] // Simurgh's Sovereign on right parent
      )

      const rightWingsTraits = [
        parentRight.Wings.p,
        parentRight.Wings.h1,
        parentRight.Wings.h2,
        parentRight.Wings.h3,
      ]
      if (rightWingsTraits.includes(traits.Wings.p)) {
        rightWingsInherited++
      }
    }

    console.log(`Right parent wings inherited: ${rightWingsInherited}/20 times`)
    assert.isAbove(rightWingsInherited, 8)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })

  test("Both parent has simurgh's sovereign", async ({ assert }) => {
    let leftWingsInherited = 0

    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10012 + i,
        1001,
        1002,
        'Gen 1',
        [[EBreedingItem.SIMURGHS_SOVEREIGN], [], [EBreedingItem.SIMURGHS_SOVEREIGN]] // Simurgh's Sovereign on both parent
      )

      const leftWingsTraits = [
        parentLeft.Wings.p,
        parentLeft.Wings.h1,
        parentLeft.Wings.h2,
        parentLeft.Wings.h3,
      ]

      const rightWingsTraits = [
        parentRight.Wings.p,
        parentRight.Wings.h1,
        parentRight.Wings.h2,
        parentRight.Wings.h3,
      ]

      if ([...leftWingsTraits, ...rightWingsTraits].includes(traits.Wings.p)) {
        leftWingsInherited++
      }
    }

    console.log(`Either parent wings inherited: ${leftWingsInherited}/20 times`)
    assert.isAbove(leftWingsInherited, 8)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })
})

test.group('Test Rainbow Rain', () => {
  let parentLeft: Traits
  let parentRight: Traits

  test('Left parent has rainbow rain', async ({ assert }) => {
    let leftColorInherited = 0

    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10014 + i,
        1001,
        1002,
        'Gen 1',
        [[EBreedingItem.RAINBOW_RAIN], [], []] // Rainbow Rain on left parent
      )

      const leftColorTraits = [
        parentLeft.Color.p,
        parentLeft.Color.h1,
        parentLeft.Color.h2,
        parentLeft.Color.h3,
      ]
      if (leftColorTraits.includes(traits.Color.p)) {
        leftColorInherited++
      }
    }

    console.log(`Left parent color inherited: ${leftColorInherited}/20 times`)
    assert.isAbove(leftColorInherited, 8)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)
    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })

  test('Right parent has rainbow rain', async ({ assert }) => {
    let rightColorInherited = 0

    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10014 + i,
        1001,
        1002,
        'Gen 1',
        [[], [], [EBreedingItem.RAINBOW_RAIN]] // Rainbow Rain on right parent
      )

      const rightColorTraits = [
        parentRight.Color.p,
        parentRight.Color.h1,
        parentRight.Color.h2,
        parentRight.Color.h3,
      ]
      if (rightColorTraits.includes(traits.Color.p)) {
        rightColorInherited++
      }
    }

    console.log(`Right parent color inherited: ${rightColorInherited}/20 times`)
    assert.isAbove(rightColorInherited, 8)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })

  test('Both parent has rainbow rain', async ({ assert }) => {
    let colorInherited = 0

    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10014 + i,
        1001,
        1002,
        'Gen 1',
        [[EBreedingItem.RAINBOW_RAIN], [], [EBreedingItem.RAINBOW_RAIN]] // Rainbow Rain on both parent
      )

      const leftColorTraits = [
        parentLeft.Color.p,
        parentLeft.Color.h1,
        parentLeft.Color.h2,
        parentLeft.Color.h3,
      ]

      const rightColorTraits = [
        parentRight.Color.p,
        parentRight.Color.h1,
        parentRight.Color.h2,
        parentRight.Color.h3,
      ]

      if ([...leftColorTraits, ...rightColorTraits].includes(traits.Color.p)) {
        colorInherited++
      }
    }

    console.log(`Either parent color inherited: ${colorInherited}/20 times`)
    assert.isAbove(colorInherited, 8)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })
})

test.group("Test St. Elmo's Fire", () => {
  let parentLeft: Traits
  let parentRight: Traits

  test("Left parent has st. elmo's fire", async ({ assert }) => {
    let leftInstinctInherited = 0

    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10013 + i,
        1001,
        1002,
        'Gen 1',
        [[EBreedingItem.ST_ELMO_FIRE], [], []] // St. Elmo's Fire on left parent
      )

      if (traits.Instinct === parentLeft.Instinct) {
        leftInstinctInherited++
      }
    }

    console.log(`Left parent instinct inherited: ${leftInstinctInherited}/20 times`)
    assert.isAtLeast(leftInstinctInherited, 4)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })

  test("Right parent has st. elmo's fire", async ({ assert }) => {
    let rightInstinctInherited = 0

    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10013 + i,
        1001,
        1002,
        'Gen 1',
        [[], [], [EBreedingItem.ST_ELMO_FIRE]] // St. Elmo's Fire on right parent
      )

      if (traits.Instinct === parentRight.Instinct) {
        rightInstinctInherited++
      }
    }

    console.log(`Right parent instinct inherited: ${rightInstinctInherited}/20 times`)
    assert.isAtLeast(rightInstinctInherited, 4)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })

  test("Both parent has st. elmo's fire", async ({ assert }) => {
    let instinctInherited = 0

    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10013 + i,
        1001,
        1002,
        'Gen 1',
        [[EBreedingItem.ST_ELMO_FIRE], [], [EBreedingItem.ST_ELMO_FIRE]] // St. Elmo's Fire on both parent
      )

      if (traits.Instinct === parentLeft.Instinct || traits.Instinct === parentRight.Instinct) {
        instinctInherited++
      }
    }

    console.log(`Either parent instinct inherited: ${instinctInherited}/20 times`)
    assert.isAtLeast(instinctInherited, 4)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })
})

test.group("Test Dip's Beak", () => {
  let parentLeft: Traits
  let parentRight: Traits

  test("Left parent has dip's beak", async ({ assert }) => {
    let leftAttackInherited = 0

    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10015 + i,
        1001,
        1002,
        'Gen 1',
        [[EBreedingItem.DIPS_BEAK], [], []] // Dip's Beak on left parent
      )

      // Check if attack value is within random factor range (-15 and +10)
      const leftAttack = parentLeft['Innate Attack']
      const offspringAttack = traits['Innate Attack']

      if (leftAttack - 15 <= offspringAttack && offspringAttack <= leftAttack + 10) {
        leftAttackInherited++
      }
    }

    console.log(`Left parent attack inherited: ${leftAttackInherited}/20 times`)
    assert.isAbove(leftAttackInherited, 10)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })

  test("Right parent has dip's beak", async ({ assert }) => {
    let rightAttackInherited = 0

    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10015 + i,
        1001,
        1002,
        'Gen 1',
        [[], [], [EBreedingItem.DIPS_BEAK]] // Dip's Beak on right parent
      )

      // Check if attack value is within random factor range (-15 and +10)
      const rightAttack = parentRight['Innate Attack']
      const offspringAttack = traits['Innate Attack']

      if (rightAttack - 15 <= offspringAttack && offspringAttack <= rightAttack + 10) {
        rightAttackInherited++
      }
    }

    console.log(`Right parent attack inherited: ${rightAttackInherited}/20 times`)
    assert.isAbove(rightAttackInherited, 10)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })

  test("Both parent has dip's beak", async ({ assert }) => {
    let attackInherited = 0

    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10015 + i,
        1001,
        1002,
        'Gen 1',
        [[EBreedingItem.DIPS_BEAK], [], [EBreedingItem.DIPS_BEAK]] // Dip's Beak on both parent
      )

      // Check if attack value is within random factor range (-15 and +10)
      const leftAttack = parentLeft['Innate Attack']
      const rightAttack = parentRight['Innate Attack']
      const offspringAttack = traits['Innate Attack']

      if (
        (leftAttack - 15 <= offspringAttack && offspringAttack <= leftAttack + 10) ||
        (rightAttack - 15 <= offspringAttack && offspringAttack <= rightAttack + 10)
      ) {
        attackInherited++
      }
    }

    console.log(`Either parent attack inherited: ${attackInherited}/20 times`)
    assert.isAbove(attackInherited, 10)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })
})

test.group('Test Pos2 Pellet', () => {
  let parentLeft: Traits
  let parentRight: Traits

  test('Left parent has pos2 pellet', async ({ assert }) => {
    let leftDefenseInherited = 0

    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10016 + i,
        1001,
        1002,
        'Gen 1',
        [[EBreedingItem.POS2_PELLET], [], []] // Pos2 Pellet on left parent
      )

      // Check if defense value is within random factor range (-15 and +10)
      const leftDefense = parentLeft['Innate Defense']
      const offspringDefense = traits['Innate Defense']

      if (leftDefense - 15 <= offspringDefense && offspringDefense <= leftDefense + 10) {
        leftDefenseInherited++
      }
    }

    console.log(`Left parent defense inherited: ${leftDefenseInherited}/20 times`)
    assert.isAbove(leftDefenseInherited, 10)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })

  test('Right parent has pos2 pellet', async ({ assert }) => {
    let rightDefenseInherited = 0

    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10016 + i,
        1001,
        1002,
        'Gen 1',
        [[], [], [EBreedingItem.POS2_PELLET]] // Pos2 Pellet on right parent
      )

      // Check if defense value is within random factor range (-15 and +10)
      const rightDefense = parentRight['Innate Defense']
      const offspringDefense = traits['Innate Defense']

      if (rightDefense - 15 <= offspringDefense && offspringDefense <= rightDefense + 10) {
        rightDefenseInherited++
      }
    }

    console.log(`Right parent defense inherited: ${rightDefenseInherited}/20 times`)
    assert.isAbove(rightDefenseInherited, 10)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })

  test('Both parent has pos2 pellet', async ({ assert }) => {
    let defenseInherited = 0

    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10016 + i,
        1001,
        1002,
        'Gen 1',
        [[EBreedingItem.POS2_PELLET], [], [EBreedingItem.POS2_PELLET]] // Pos2 Pellet on both parent
      )
      // Check if defense value is within random factor range (-15 and +10)
      const leftDefense = parentLeft['Innate Defense']
      const rightDefense = parentRight['Innate Defense']
      const offspringDefense = traits['Innate Defense']

      if (
        (leftDefense - 15 <= offspringDefense && offspringDefense <= leftDefense + 10) ||
        (rightDefense - 15 <= offspringDefense && offspringDefense <= rightDefense + 10)
      ) {
        defenseInherited++
      }
    }

    console.log(`Either parent defense inherited: ${defenseInherited}/20 times`)
    assert.isAbove(defenseInherited, 10)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })
})

test.group('Test Fetzzz Feet', () => {
  let parentLeft: Traits
  let parentRight: Traits

  test('Left parent has fetzzz feet', async ({ assert }) => {
    let leftSpeedInherited = 0

    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10017 + i,
        1001,
        1002,
        'Gen 1',
        [[EBreedingItem.FETZZZ_FEET], [], []] // Fetzzz Feet on left parent
      )

      // Check if speed value is within random factor range (-15 and +10)
      const leftSpeed = parentLeft['Innate Speed']
      const offspringSpeed = traits['Innate Speed']

      if (leftSpeed - 15 <= offspringSpeed && offspringSpeed <= leftSpeed + 10) {
        leftSpeedInherited++
      }
    }

    console.log(`Left parent speed inherited: ${leftSpeedInherited}/20 times`)
    assert.isAbove(leftSpeedInherited, 10)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })

  test('Right parent has fetzzz feet', async ({ assert }) => {
    let rightSpeedInherited = 0

    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10017 + i,
        1001,
        1002,
        'Gen 1',
        [[], [], [EBreedingItem.FETZZZ_FEET]] // Fetzzz Feet on right parent
      )

      // Check if speed value is within random factor range (-15 and +10)
      const rightSpeed = parentRight['Innate Speed']
      const offspringSpeed = traits['Innate Speed']

      if (rightSpeed - 15 <= offspringSpeed && offspringSpeed <= rightSpeed + 10) {
        rightSpeedInherited++
      }
    }

    console.log(`Right parent speed inherited: ${rightSpeedInherited}/20 times`)
    assert.isAbove(rightSpeedInherited, 10)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })

  test('Both parent has fetzzz feet', async ({ assert }) => {
    let speedInherited = 0

    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10017 + i,
        1001,
        1002,
        'Gen 1',
        [[EBreedingItem.FETZZZ_FEET], [], [EBreedingItem.FETZZZ_FEET]] // Fetzzz Feet on both parent
      )

      // Check if speed value is within random factor range (-15 and +10)
      const leftSpeed = parentLeft['Innate Speed']
      const rightSpeed = parentRight['Innate Speed']
      const offspringSpeed = traits['Innate Speed']

      if (
        (leftSpeed - 15 <= offspringSpeed && offspringSpeed <= leftSpeed + 10) ||
        (rightSpeed - 15 <= offspringSpeed && offspringSpeed <= rightSpeed + 10)
      ) {
        speedInherited++
      }
    }

    console.log(`Either parent speed inherited: ${speedInherited}/20 times`)
    assert.isAbove(speedInherited, 10)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })
})

test.group("Test Vananderen's Vitality", () => {
  let parentLeft: Traits
  let parentRight: Traits

  test("Left parent has Vananderen's Vitality", async ({ assert }) => {
    let leftHealthInherited = 0

    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10018 + i,
        1001,
        1002,
        'Gen 1',
        [[EBreedingItem.VANDERERENS_VITALITY], [], []] // Vananderen's Vitality on left parent
      )

      // Check if health value is within random factor range (-15 and +10)
      const leftHealth = parentLeft['Innate Health']
      const offspringHealth = traits['Innate Health']

      if (leftHealth - 15 <= offspringHealth && offspringHealth <= leftHealth + 10) {
        leftHealthInherited++
      }
    }

    console.log(`Left parent health inherited: ${leftHealthInherited}/20 times`)
    assert.isAbove(leftHealthInherited, 10)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })

  test("Right parent has Vananderen's Vitality", async ({ assert }) => {
    let rightHealthInherited = 0

    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10018 + i,
        1001,
        1002,
        'Gen 1',
        [[], [], [EBreedingItem.VANDERERENS_VITALITY]] // Vananderen's Vitality on right parent
      )

      // Check if health value is within random factor range (-15 and +10)
      const rightHealth = parentRight['Innate Health']
      const offspringHealth = traits['Innate Health']

      if (rightHealth - 15 <= offspringHealth && offspringHealth <= rightHealth + 10) {
        rightHealthInherited++
      }
    }

    console.log(`Right parent health inherited: ${rightHealthInherited}/20 times`)
    assert.isAbove(rightHealthInherited, 10)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })

  test("Both parent has Vananderen's Vitality", async ({ assert }) => {
    let healthInherited = 0

    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10018 + i,
        1001,
        1002,
        'Gen 1',
        [[EBreedingItem.VANDERERENS_VITALITY], [], [EBreedingItem.VANDERERENS_VITALITY]] // Vananderen's Vitality on both parent
      )

      // Check if health value is within random factor range (-15 and +10)
      const leftHealth = parentLeft['Innate Health']
      const rightHealth = parentRight['Innate Health']
      const offspringHealth = traits['Innate Health']

      if (
        (leftHealth - 15 <= offspringHealth && offspringHealth <= leftHealth + 10) ||
        (rightHealth - 15 <= offspringHealth && offspringHealth <= rightHealth + 10)
      ) {
        healthInherited++
      }
    }

    console.log(`Either parent health inherited: ${healthInherited}/20 times`)
    assert.isAbove(healthInherited, 10)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })
})

test.group("Test Pinong's Bird", () => {
  let parentLeft: Traits
  let parentRight: Traits

  test("Left parent has Pinong's Bird", async ({ assert }) => {
    let leftCockrageInherited = 0

    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10019 + i,
        1001,
        1002,
        'Gen 1',
        [[EBreedingItem.PINONGS_BIRD], [], []] // Pinong's Bird on left parent
      )

      // Check if cockrage value is within random factor range (-15 and +10)
      const leftCockrage = parentLeft['Innate Cockrage']
      const offspringCockrage = traits['Innate Cockrage']

      if (leftCockrage - 15 <= offspringCockrage && offspringCockrage <= leftCockrage + 10) {
        leftCockrageInherited++
      }
    }

    console.log(`Left parent cockrage inherited: ${leftCockrageInherited}/20 times`)
    assert.isAbove(leftCockrageInherited, 10)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })

  test("Right parent has Pinong's Bird", async ({ assert }) => {
    let rightCockrageInherited = 0

    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10019 + i,
        1001,
        1002,
        'Gen 1',
        [[], [], [EBreedingItem.PINONGS_BIRD]] // Pinong's Bird on right parent
      )

      // Check if cockrage value is within random factor range (-15 and +10)
      const rightCockrage = parentRight['Innate Cockrage']
      const offspringCockrage = traits['Innate Cockrage']

      if (rightCockrage - 15 <= offspringCockrage && offspringCockrage <= rightCockrage + 10) {
        rightCockrageInherited++
      }
    }

    console.log(`Right parent cockrage inherited: ${rightCockrageInherited}/20 times`)
    assert.isAbove(rightCockrageInherited, 10)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })

  test("Both parent has Pinong's Bird", async ({ assert }) => {
    let cockrageInherited = 0

    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10019 + i,
        1001,
        1002,
        'Gen 1',
        [[EBreedingItem.PINONGS_BIRD], [], [EBreedingItem.PINONGS_BIRD]] // Pinong's Bird on both parent
      )

      // Check if cockrage value is within random factor range (-15 and +10)
      const leftCockrage = parentLeft['Innate Cockrage']
      const rightCockrage = parentRight['Innate Cockrage']
      const offspringCockrage = traits['Innate Cockrage']

      if (
        (leftCockrage - 15 <= offspringCockrage && offspringCockrage <= leftCockrage + 10) ||
        (rightCockrage - 15 <= offspringCockrage && offspringCockrage <= rightCockrage + 10)
      ) {
        cockrageInherited++
      }
    }

    console.log(`Either parent cockrage inherited: ${cockrageInherited}/20 times`)
    assert.isAbove(cockrageInherited, 10)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })
})

test.group("Test Ouchie's Ornament", () => {
  let parentLeft: Traits
  let parentRight: Traits

  test("Left parent has Ouchie's Ornament", async ({ assert }) => {
    let leftFerocityInherited = 0

    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10020 + i,
        1001,
        1002,
        'Gen 1',
        [[EBreedingItem.OUCHIES_ORNAMENT], [], []] // Ouchie's Ornament on left parent
      )

      // Check if ferocity value is within random factor range (-15 and +10)
      const leftFerocity = parentLeft['Innate Ferocity']
      const offspringFerocity = traits['Innate Ferocity']

      if (leftFerocity - 15 <= offspringFerocity && offspringFerocity <= leftFerocity + 10) {
        leftFerocityInherited++
      }
    }

    console.log(`Left parent ferocity inherited: ${leftFerocityInherited}/20 times`)
    assert.isAbove(leftFerocityInherited, 10)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })

  test("Right parent has Ouchie's Ornament", async ({ assert }) => {
    let rightFerocityInherited = 0

    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10020 + i,
        1001,
        1002,
        'Gen 1',
        [[], [], [EBreedingItem.OUCHIES_ORNAMENT]] // Ouchie's Ornament on right parent
      )

      // Check if ferocity value is within random factor range (-15 and +10)
      const rightFerocity = parentRight['Innate Ferocity']
      const offspringFerocity = traits['Innate Ferocity']

      if (rightFerocity - 15 <= offspringFerocity && offspringFerocity <= rightFerocity + 10) {
        rightFerocityInherited++
      }
    }

    console.log(`Right parent ferocity inherited: ${rightFerocityInherited}/20 times`)
    assert.isAbove(rightFerocityInherited, 10)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })

  test("Both parent has Ouchie's Ornament", async ({ assert }) => {
    let ferocityInherited = 0

    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10020 + i,
        1001,
        1002,
        'Gen 1',
        [[EBreedingItem.OUCHIES_ORNAMENT], [], [EBreedingItem.OUCHIES_ORNAMENT]] // Ouchie's Ornament on both parent
      )

      // Check if ferocity value is within random factor range (-15 and +10)
      const leftFerocity = parentLeft['Innate Ferocity']
      const rightFerocity = parentRight['Innate Ferocity']
      const offspringFerocity = traits['Innate Ferocity']

      if (
        (leftFerocity - 15 <= offspringFerocity && offspringFerocity <= leftFerocity + 10) ||
        (rightFerocity - 15 <= offspringFerocity && offspringFerocity <= rightFerocity + 10)
      ) {
        ferocityInherited++
      }
    }

    console.log(`Either parent ferocity inherited: ${ferocityInherited}/20 times`)
    assert.isAbove(ferocityInherited, 10)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })
})

test.group('Test Lockedin State', () => {
  let parentLeft: Traits
  let parentRight: Traits

  test('Left parent has Lockedin State', async ({ assert }) => {
    let leftEvasionInherited = 0

    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10021 + i,
        1001,
        1002,
        'Gen 1',
        [[EBreedingItem.LOCKED_IN_STATE], [], []] // Lockedin State on left parent
      )

      // Check if evasion value is within random factor range (-15 and +10)
      const leftEvasion = parentLeft['Innate Evasion']
      const offspringEvasion = traits['Innate Evasion']

      if (leftEvasion - 15 <= offspringEvasion && offspringEvasion <= leftEvasion + 10) {
        leftEvasionInherited++
      }
    }

    console.log(`Left parent evasion inherited: ${leftEvasionInherited}/20 times`)
    assert.isAbove(leftEvasionInherited, 10)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })

  test('Right parent has Lockedin State', async ({ assert }) => {
    let rightEvasionInherited = 0

    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10021 + i,
        1001,
        1002,
        'Gen 1',
        [[], [], [EBreedingItem.LOCKED_IN_STATE]] // Lockedin State on right parent
      )

      // Check if evasion value is within random factor range (-15 and +10)
      const rightEvasion = parentRight['Innate Evasion']
      const offspringEvasion = traits['Innate Evasion']

      if (rightEvasion - 15 <= offspringEvasion && offspringEvasion <= rightEvasion + 10) {
        rightEvasionInherited++
      }
    }

    console.log(`Right parent evasion inherited: ${rightEvasionInherited}/20 times`)
    assert.isAbove(rightEvasionInherited, 10)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })

  test('Both parent has Lockedin State', async ({ assert }) => {
    let evasionInherited = 0

    for (let i = 0; i < 20; i++) {
      const { traits } = await breed(
        parentLeft,
        parentRight,
        10021 + i,
        1001,
        1002,
        'Gen 1',
        [[EBreedingItem.LOCKED_IN_STATE], [], [EBreedingItem.LOCKED_IN_STATE]] // Lockedin State on both parent
      )

      // Check if evasion value is within random factor range (-15 and +10)
      const leftEvasion = parentLeft['Innate Evasion']
      const rightEvasion = parentRight['Innate Evasion']
      const offspringEvasion = traits['Innate Evasion']

      if (
        (leftEvasion - 15 <= offspringEvasion && offspringEvasion <= leftEvasion + 10) ||
        (rightEvasion - 15 <= offspringEvasion && offspringEvasion <= rightEvasion + 10)
      ) {
        evasionInherited++
      }
    }

    console.log(`Either parent evasion inherited: ${evasionInherited}/20 times`)
    assert.isAbove(evasionInherited, 10)
  }).setup(async () => {
    const chickenLeftTokenId = 1001
    const chickenRightTokenId = 1002

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    parentLeft = decodeGene(findChickenLeft.genes) as Traits
    parentRight = decodeGene(findChickenRight.genes) as Traits
  })
})
