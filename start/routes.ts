/*
|--------------------------------------------------------------------------
| Routes
|--------------------------------------------------------------------------
|
| This file is dedicated for defining HTTP routes. A single file is enough
| for majority of projects, however you can define routes in different
| files and just make sure to import them inside this file. For example
|
| Define routes in following two files
| ├── start/routes/cart.ts
| ├── start/routes/customer.ts
|
| and then import them inside `start/routes.ts` as follows
|
| import './routes/cart'
| import './routes/customer'
|
*/

import Route from '@ioc:Adonis/Core/Route'

Route.get('/', async () => {
  return { hello: 'world' }
})

Route.get('/blockchain', 'ApisController.blockchain')

Route.post('/auth/request', 'AuthController.request')
Route.post('/auth/verify', 'AuthController.verify')

Route.get('/breeding-cooldowns', 'BreedingCooldownsController.viewAll')

Route.get('/api/image/:filename', 'ApisController.image')
Route.get('/api/:filename', 'ApisController.metadata')

Route.group(() => {
  Route.post('/breeding-cooldowns/create', 'BreedingCooldownsController.create')
  Route.post('/breeding-cooldowns/update', 'BreedingCooldownsController.update')
  Route.post('/breeding-cooldowns/delete', 'BreedingCooldownsController.delete')
  Route.get('/breeding-cooldowns', 'BreedingCooldownsController.viewAll')

  Route.post('/breeding-fees/update', 'BreedingFeesController.update')
  Route.get('/breeding-fees', 'BreedingFeesController.viewAll')
})
  .prefix('/admin')
  .middleware(['sabongauth', 'admin'])

Route.group(() => {
  Route.get('/me', 'ApisController.me')

  // BREEDING
  Route.post('/breedings/initiate-breeding', 'BreedingsController.initiateBreeding')
  Route.post('/breedings/hatch', 'BreedingsController.hatch')
  Route.post('/breedings/initiate-bulk-breeding', 'BreedingsController.initiateBulkBreeding')
  Route.get('/breedings/view-breeding-history', 'BreedingsController.viewBreedingHistory')

  // BREEDING MAINTENANCE
  // Route.post('/breedings/initiate-breeding', 'BreedingsController.maintenanceBreeding')
  // Route.post('/breedings/hatch', 'BreedingsController.hatch')
  // Route.post('/breedings/initiate-bulk-breeding', 'BreedingsController.maintenanceBreeding')
  // Route.get('/breedings/view-breeding-history', 'BreedingsController.viewBreedingHistory')

  // NINUNO REWARDS
  Route.post(
    '/ninuno-rewards/transfer-balance',
    'NinunoRewardsController.transferNinunoRewardsToBalance'
  )
  Route.get(
    '/ninuno-rewards/transfer-balance-logs',
    'NinunoRewardsController.viewTransferNinunoRewardsAll'
  )
  Route.post('/ninuno-rewards/view-chicken-info', 'NinunoRewardsController.viewChickenInfo')
  Route.post(
    '/ninuno-rewards/initiate-claim-request',
    'NinunoRewardsController.initiateClaimRequest'
  )
  Route.get('/ninuno-rewards/view-claim-request', 'NinunoRewardsController.viewClaimRequest')
  Route.post(
    '/ninuno-rewards/reinitiate-claim-request',
    'NinunoRewardsController.reinitiateClaimRequest'
  )

  // RENTALS
  Route.post('/rentals/create', 'RentalsController.createRental')
  Route.post('/rentals/create-bulk', 'RentalsController.createRentalBulk')
  Route.get('/rentals/available', 'RentalsController.listAvailableRentals')
  Route.post('/rentals/rent', 'RentalsController.rentChicken')
  Route.get('/rentals/my-rentals', 'RentalsController.myRentals')
  Route.get('/rentals/history', 'RentalsController.rentalHistory')
  Route.post('/rentals/cancel', 'RentalsController.cancelRental')
  Route.post('/rentals/cancel-bulk', 'RentalsController.cancelRentalBulk')

  //BATTLE
  Route.post('/battle/request', 'BattlesController.request')
  Route.post('/battle/verify', 'BattlesController.verify')

  // NINUNO REWARDS MAINTENANCE
  // Route.post('/ninuno-rewards/transfer-balance', 'NinunoRewardsController.maintenanceNinunoRewards')
  // Route.get(
  //   '/ninuno-rewards/transfer-balance-logs',
  //   'NinunoRewardsController.viewTransferNinunoRewardsAll'
  // )
  // Route.post('/ninuno-rewards/view-chicken-info', 'NinunoRewardsController.viewChickenInfo')
  // Route.post(
  //   '/ninuno-rewards/initiate-claim-request',
  //   'NinunoRewardsController.maintenanceNinunoRewards'
  // )
  // Route.get('/ninuno-rewards/view-claim-request', 'NinunoRewardsController.viewClaimRequest')
  // Route.post(
  //   '/ninuno-rewards/reinitiate-claim-request',
  //   'NinunoRewardsController.maintenanceNinunoRewards'
  // )
}).middleware(['sabongauth'])

Route.get('/breeding-fees', 'BreedingFeesController.viewAll')
Route.post('/breedings/resync', 'BreedingsController.resync')

// Public rental routes
Route.get('/rentals/chicken/:chickenTokenId', 'RentalsController.getChickenRental')
Route.post('/rentals/chickens-info-bulk', 'RentalsController.getChickenRentalsBulk')
Route.get('/rentals/chickens-by-wallet', 'RentalsController.getChickenRentalsByWallet')

Route.post('/chickens/list/owned-and-rented', 'ApisController.listOwnedAndRentedChickens')
Route.post('/chickens/check/owned-or-rented', 'ApisController.checkOwnedOrRented')
