import BreedingEventService from 'App/Services/BreedingEventService'
import RentalEventService from 'App/Services/RentalEventService'
import Logger from '@ioc:Adonis/Core/Logger'
import Bull from '@ioc:Rocketseat/Bull'
import ProcessCockConversionJob from 'App/Jobs/ProcessCockConversionJob'

async function registerProcessCockConversionJob() {
  await Bull.add(
    new ProcessCockConversionJob().key,
    {},
    {
      repeat: { cron: '0 0 * * *' },
    }
  )
}

async function startBreedingEventWatcher() {
  try {
    Logger.info('Started watching breeding events')
    await BreedingEventService.watchBreedingEvents()
  } catch (error) {
    Logger.error(`Failed to start watching breeding events: ${error.message}`)
  }
}

async function startRentalEventWatcher() {
  try {
    Logger.info('Started watching rental events')
    await RentalEventService.watchRentalEvents()
  } catch (error) {
    Logger.error(`Failed to start watching rental events: ${error.message}`)
  }
}

registerProcessCockConversionJob()
startBreedingEventWatcher()
startRentalEventWatcher()
