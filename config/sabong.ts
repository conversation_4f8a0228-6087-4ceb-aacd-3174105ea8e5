import Env from '@ioc:Adonis/Core/Env'
import { chickenGenesis<PERSON>bi } from './abi/chicken-genesis-abi'
import { chickenLegacy<PERSON>bi } from './abi/chicken-legacy-abi'
import { cockAbi } from './abi/cock-abi'
import { itemAbi } from './abi/item-abi'
import { breedingAbi } from './abi/breeding-abi'
import { resourcesAbi } from './abi/resources-abi'
import { referralAbi } from './abi/referral-abi'
import { rentalAbi } from './abi/rental-abi'
import { bulkFetcherAbi } from './abi/bulk-fetcher-abi'

export enum EBreedingItem {
  SOULKNOT = 63,
  RANDOMIZER = 64,
  GREGORS_GIFT = 65,
  MENDELS_MEMENTO = 66,
  QUENTINS_TALON = 67,
  DRAGONS_WHIP = 68,
  CHIBIDEIS_CURSE = 69,
  ALL_SEEING_SEED = 70,
  CHIM_LACS_CURIO = 71,
  SUAVE_SCISSORS = 72,
  SIMURGHS_SOVEREIGN = 73,
  ST_ELMO_FIRE = 74,
  RAINBOW_RAIN = 75,
  DIPS_BEAK = 76,
  POS2_PELLET = 77,
  FETZZZ_FEET = 78,
  VANDERERENS_VITALITY = 79,
  PINONGS_BIRD = 80,
  OUCHIES_ORNAMENT = 81,
  LOCKED_IN_STATE = 82,
  COMMON_INCUBATOR = 83,
  ARCANE_INCUBATOR = 84,
  SELENIUM = 85,
  SELENIUM_PRO = 86,
}

const SABONG_CONFIG = {
  ENV: Env.get('NODE_ENV'),
  API_URL: Env.get('API_URL'),
  ADMIN_ADDRESSES: Env.get('ADMIN_ADDRESSES')
    .split(',')
    .map((address) => address.toLowerCase()) as string[],
  SIGNER_KEY: Env.get('SIGNER_KEY'),
  RONIN_RPC: Env.get('RONIN_RPC'),
  CONTRACTS: {
    CHICKEN_GENESIS_ADDRESS: Env.get('CHICKEN_GENESIS_ADDRESS'),
    CHICKEN_LEGACY_ADDRESS: Env.get('CHICKEN_LEGACY_ADDRESS'),
    COCK_ADDRESS: Env.get('COCK_ADDRESS'),
    ITEMS_ADDRESS: Env.get('ITEMS_ADDRESS'),
    BREEDING_ADDRESS: Env.get('BREEDING_ADDRESS'),
    RESOURCES_ADDRESS: Env.get('RESOURCES_ADDRESS'),
    REFERRAL_ADDRESS: Env.get('REFERRAL_ADDRESS'),
    RENTAL_ADDRESS: Env.get('RENTAL_ADDRESS'),
    BULK_FETCHER_ADDRESS: Env.get('BULK_FETCHER_ADDRESS'),
  },
  ABIS: {
    CHICKEN_GENESIS_ABI: chickenGenesisAbi,
    CHICKEN_LEGACY_ABI: chickenLegacyAbi,
    COCK_ABI: cockAbi,
    ITEMS_ABI: itemAbi,
    BREEDING_ABI: breedingAbi,
    RESOURCES_ABI: resourcesAbi,
    REFERRAL_ABI: referralAbi,
    RENTAL_ABI: rentalAbi,
    BULK_FETCHER_ABI: bulkFetcherAbi,
  },
  BREEDING_COOLDOWN_MAX_COUNT_DURATION: Number(Env.get('BREEDING_COOLDOWN_MAX_COUNT_DURATION')),
  BREEDING_COOLDOWN_MAX_COUNT_FEE: Number(Env.get('BREEDING_COOLDOWN_MAX_COUNT_FEE')),
  BREEDING_NINUNO_PERCENTAGE_PER_PARENT: Number(Env.get('BREEDING_NINUNO_PERCENTAGE_PER_PARENT')),
  BREEDING_HATCHING_DAYS: Number(Env.get('BREEDING_HATCHING_DAYS')),
  CHICKEN_GENESIS_THRESHOLD: 2222,
  CHICKEN_LEGACY_THRESHOLD: 11110,
  BREEDING_CONFIG: {
    TRAITS_WEIGHTS: [37.5, 37.5, 9.4, 9.4, 2.3, 2.3, 0.8, 0.8], //[p,p,h1,h1,h2,h2,h3,h3]
    NINUNO_DISTRIBUTION_WEIGHTS: [10, 5, 3, 2, 1], //[3x Legendary, 2x Legendary, 1x Legendary, Genesis, Legacy]
  },
  BREEDING_CREATION_BLOCKNUMBER: BigInt(Env.get('BREEDING_CREATION_BLOCKNUMBER')),
  CHICKEN_IVORY_API_URL: Env.get('CHICKEN_IVORY_API_URL'),
  CHICKEN_IVORY_API_KEY: Env.get('CHICKEN_IVORY_API_KEY'),
  SKYMAVIS_API_KEY: Env.get('SKYMAVIS_API_KEY'),
  RENTAL_CREATION_BLOCKNUMBER: BigInt(Env.get('RENTAL_CREATION_BLOCKNUMBER', '0')),
  BREEDING_ITEMS: [
    {
      tokenId: EBreedingItem.SOULKNOT,
      name: 'Soulknot',
      slug: 'soulknot',
      description: '25% chance to Inherit 3 of 7 random Innate Points from parent',
    },
    {
      tokenId: EBreedingItem.RANDOMIZER,
      name: 'Randomizer',
      slug: 'randomizer',
      description: 'Chicken has Random P, H1, H2, H3 Genes',
    },
    {
      tokenId: EBreedingItem.GREGORS_GIFT,
      name: "Gregor's Gift",
      slug: 'gregors-gift',
      description: "Increase Parent's Primary Genes Inheritance chance by 25%",
    },
    {
      tokenId: EBreedingItem.MENDELS_MEMENTO,
      name: "Mendel's Memento",
      slug: 'mendels-memento',
      description: "Increase Parent's recessive Gene influence by 25%",
    },
    {
      tokenId: EBreedingItem.QUENTINS_TALON,
      name: "Quentin's Talon",
      slug: 'quentins-talon',
      description: "Increase Parent's Feet Genes Inheritance chance by 25%",
    },
    {
      tokenId: EBreedingItem.DRAGONS_WHIP,
      name: "Dragon's Whip",
      slug: 'dragons-whip',
      description: "Increase Parent's Tail Genes Inheritance chance by 25%",
    },
    {
      tokenId: EBreedingItem.CHIBIDEIS_CURSE,
      name: "Chibidei's Curse",
      slug: 'chibideis-curse',
      description: "Increase Parent's Body Genes Inheritance chance by 25%",
    },
    {
      tokenId: EBreedingItem.ALL_SEEING_SEED,
      name: 'All-seeing Seed',
      slug: 'all-seeing-seed',
      description: "Increase Parent's Eyes Genes Inheritance chance by 25%",
    },
    {
      tokenId: EBreedingItem.CHIM_LACS_CURIO,
      name: "Chim Lac's Curio",
      slug: 'chim-lacs-curio',
      description: "Increase Parent's Beak Genes Inheritance chance by 25%",
    },
    {
      tokenId: EBreedingItem.SUAVE_SCISSORS,
      name: 'Suave Scissors',
      slug: 'suave-scissors',
      description: "Increase Parent's Comb Genes Inheritance chance by 25%",
    },
    {
      tokenId: EBreedingItem.SIMURGHS_SOVEREIGN,
      name: "Simurgh's Sovereign",
      slug: 'simurghs-sovereign',
      description: "Increase Parent's Wings Genes Inheritance chance by 25%",
    },
    {
      tokenId: EBreedingItem.ST_ELMO_FIRE,
      name: "St. Elmo's Fire",
      slug: 'st-elmos-fire',
      description:
        "Increase Parent's Instinct inheritance chance by 25% (Additional 6.25% to parent's base chance)",
    },
    {
      tokenId: EBreedingItem.RAINBOW_RAIN,
      name: 'Rainbow Rain',
      slug: 'rainbow-rain',
      description: "Increase Parent's Color Genes Inheritance chance by 25%",
    },
    {
      tokenId: EBreedingItem.DIPS_BEAK,
      name: "Dip's Beak",
      slug: 'dips-beak',
      description: "Increase Parent's Attack IP Genes Inheritance chance by 25%",
    },
    {
      tokenId: EBreedingItem.POS2_PELLET,
      name: 'Pos2 Pellet',
      slug: 'pos2-pellet',
      description: "Increase Parent's Defense IP Genes Inheritance chance by 25%",
    },
    {
      tokenId: EBreedingItem.FETZZZ_FEET,
      name: 'Fetzzz Feet',
      slug: 'fetzzz-feet',
      description: "Increase Parent's Speed IP Genes Inheritance chance by 25%",
    },
    {
      tokenId: EBreedingItem.VANDERERENS_VITALITY,
      name: "Vananderen's Vitality",
      slug: 'vandererens-vitality',
      description: "Increase Parent's Health IP Genes Inheritance chance by 25%",
    },
    {
      tokenId: EBreedingItem.PINONGS_BIRD,
      name: "Pinong's Bird",
      slug: 'pinongs-bird',
      description: "25% chance to influence Cockrage based on Parent's stat",
    },
    {
      tokenId: EBreedingItem.OUCHIES_ORNAMENT,
      name: "Ouchie's Ornament",
      slug: 'ouchies-ornament',
      description: "25% chance to influence Ferocity based on Parent's stat",
    },
    {
      tokenId: EBreedingItem.LOCKED_IN_STATE,
      name: 'Lockedin State',
      slug: 'lockedin-state',
      description: "25% chance to influence Evasion based on Parent's stat",
    },
    {
      tokenId: EBreedingItem.COMMON_INCUBATOR,
      name: 'Common Incubator',
      slug: 'common-incubator',
      description: 'Decrease hatching time by 25%',
    },
    {
      tokenId: EBreedingItem.ARCANE_INCUBATOR,
      name: 'Arcane Incubator',
      slug: 'arcane-incubator',
      description: 'Instant Hatch',
    },
    {
      tokenId: EBreedingItem.SELENIUM,
      name: 'Selenium',
      slug: 'selenium',
      description: 'Decrease Parent Breeding state by 25%',
    },
    {
      tokenId: EBreedingItem.SELENIUM_PRO,
      name: 'Selenium Pro',
      slug: 'selenium-pro',
      description: 'Instant breed',
    },
  ],
}

export default SABONG_CONFIG
