import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'rentals'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.boolean('insurance_claimed').defaultTo(false).notNullable()
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('insurance_claimed')
    })
  }
}
