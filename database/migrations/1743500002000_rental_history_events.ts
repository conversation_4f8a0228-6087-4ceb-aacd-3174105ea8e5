import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'rental_history_events'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.integer('rental_id').notNullable()
      table.string('event_type').notNullable() // 'listed', 'unlisted', 'rented', 'expired', 'cancelled', 'insurance_claimed', 'price_updated'
      table.string('actor_address').notNullable() // Who performed the action
      table.json('event_data').nullable() // Additional event-specific data
      table.string('block_number').nullable() // For blockchain events
      table.string('transaction_hash').nullable() // For blockchain events
      table.text('description').nullable() // Human-readable description

      // Indexes for performance
      table.index(['rental_id'])
      table.index(['event_type'])
      table.index(['actor_address'])
      table.index(['created_at'])
      table.index(['rental_id', 'event_type'])
      table.index(['actor_address', 'event_type'])

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
