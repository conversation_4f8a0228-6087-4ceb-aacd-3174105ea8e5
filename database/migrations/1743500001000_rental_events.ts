import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'rental_events'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.string('address').notNullable()
      table.string('block_hash').notNullable()
      table.string('block_number').notNullable()
      table.text('data').notNullable()
      table.integer('log_index').notNullable()
      table.string('transaction_hash').notNullable()
      table.integer('transaction_index').notNullable()
      table.boolean('removed').defaultTo(false)
      table.json('args').notNullable()
      table.string('event_name').notNullable()
      table.integer('processed').defaultTo(0) // 0: pending, 1: processed, 2: failed

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}