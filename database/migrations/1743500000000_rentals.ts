import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'rentals'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.integer('chicken_token_id').notNullable()
      table.string('owner_address').notNullable()
      table.string('renter_address').nullable()
      table.string('ronin_price').notNullable()
      table.integer('rental_period').notNullable() // in seconds
      table.timestamp('rented_at').nullable()
      table.timestamp('expires_at').nullable()
      table.integer('status').defaultTo(0) // 0: available, 1: rented, 2: expired, 3: cancelled
      table.string('signature').nullable()
      table.integer('reward_distribution').defaultTo(1) // 1: DELEGATOR_ONLY, 2: DELEGATEE_ONLY, 3: SHARED
      table.integer('delegated_task').defaultTo(3) // 1: DAILY_RUB, 2: GAMEPLAY, 3: BOTH
      table.integer('shared_reward_amount').nullable() // Integer amount for SHARED reward distribution

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
