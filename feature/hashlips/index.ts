import SABONG_CONFIG, { EBreedingItem } from 'Config/sabong'
import { generate } from './generator'
import { getRandomNonLegendaryTrait } from 'App/Helper/chicken'

export type TraitCategory = 'Feet' | 'Tail' | 'Body' | 'Wings' | 'Eyes' | 'Beak' | 'Comb' | 'Color'

export type Traits = Record<TraitCategory, { p: string; h1: string; h2: string; h3: string }> &
  Record<
    | 'Innate Attack'
    | 'Innate Defense'
    | 'Innate Speed'
    | 'Innate Health'
    | 'Innate Ferocity'
    | 'Innate Cockrage'
    | 'Innate Evasion',
    number
  > &
  Record<'Instinct', string>

export type SpecialEffects = {
  leftParent: {
    soulknotActive: boolean
    randomizerActive: boolean
    traitBoosts: Record<string, number>
    ipBoosts: Record<string, number>
  }
  rightParent: {
    soulknotActive: boolean
    randomizerActive: boolean
    traitBoosts: Record<string, number>
    ipBoosts: Record<string, number>
  }
  global: {
    instantHatch: boolean
    instantBreed: boolean
    hatchTimeReduction: number
    breedTimeReduction: number
  }
}

const legendaryTraitMappings = {
  Beak: [
    'Chim Lạc',
    'Thunderbird',
    'Adarna',
    'Sarimanok', // Legendary traits (0-3)
  ],
  Body: [] as string[],
  Comb: [
    'Minokawa',
    'Adarna',
    'Garuda',
    'Simurgh', // Legendary traits (0-3)
  ],
  Eyes: [
    'Garuda',
    'Minokawa',
    'Adarna',
    'Sarimanok', // Legendary traits (0-3)
  ],
  Feet: [
    'Buakaw',
    'Alicanto Oro',
    'Alicanto Plata',
    'Thunderbird', // Legendary traits (0-3)
  ],
  Tail: [
    'Simurgh',
    'Chim Lạc',
    'Minokawa',
    'Adarna', // Legendary traits (0-3)
  ],
  Wings: [
    'Adarna',
    'Minokawa',
    'Garuda',
    'Simurgh', // Legendary traits (0-3)
  ],
  Color: [] as string[],
  Instinct: [] as string[],
}

// Helper function to extract IP stats from a chicken's traits
const extractIPStats = (traits) => {
  return [
    { trait_type: 'Innate Attack', value: traits['Innate Attack'], display_type: 'number' },
    { trait_type: 'Innate Defense', value: traits['Innate Defense'], display_type: 'number' },
    { trait_type: 'Innate Speed', value: traits['Innate Speed'], display_type: 'number' },
    { trait_type: 'Innate Health', value: traits['Innate Health'], display_type: 'number' },
    { trait_type: 'Innate Ferocity', value: traits['Innate Ferocity'], display_type: 'number' },
    { trait_type: 'Innate Cockrage', value: traits['Innate Cockrage'], display_type: 'number' },
    { trait_type: 'Innate Evasion', value: traits['Innate Evasion'], display_type: 'number' },
  ]
}

// Add function to apply breeding items effects
function applyBreedingItemsEffects(
  chickenLeft: Traits,
  chickenRight: Traits,
  breedingItems: number[][] = [[], [], []] // Array of arrays: [leftParentItems, bothParentsItems, rightParentItems]
): { modifiedLeft: Traits; modifiedRight: Traits; specialEffects: SpecialEffects } {
  const modifiedLeft = { ...chickenLeft }
  const modifiedRight = { ...chickenRight }
  const specialEffects = {
    leftParent: {
      soulknotActive: false,
      randomizerActive: false,
      traitBoosts: {} as Record<string, number>,
      ipBoosts: {} as Record<string, number>,
    },
    rightParent: {
      soulknotActive: false,
      randomizerActive: false,
      traitBoosts: {} as Record<string, number>,
      ipBoosts: {} as Record<string, number>,
    },
    global: {
      instantHatch: false,
      instantBreed: false,
      hatchTimeReduction: 0,
      breedTimeReduction: 0,
    },
  }

  // Apply items to left parent
  if (breedingItems[0]) {
    breedingItems[0].forEach((itemId) => {
      const item = SABONG_CONFIG.BREEDING_ITEMS.find((i) => i.tokenId === itemId)
      if (!item) return

      switch (itemId) {
        case EBreedingItem.SOULKNOT: // Soulknot
          specialEffects.leftParent.soulknotActive = true
          break
        case EBreedingItem.RANDOMIZER: // Randomizer
          specialEffects.leftParent.randomizerActive = true
          break
        case EBreedingItem.GREGORS_GIFT: // Gregor's Gift - primary genes +25%
          specialEffects.leftParent.traitBoosts.primary = 0.25
          break
        case EBreedingItem.MENDELS_MEMENTO: // Mendel's Memento - recessive gene +25%
          specialEffects.leftParent.traitBoosts.recessive = 0.25
          break
        case EBreedingItem.QUENTINS_TALON: // Quentin's Talon - Feet +25%
          specialEffects.leftParent.traitBoosts.Feet = 0.25
          break
        case EBreedingItem.DRAGONS_WHIP: // Dragon's Whip - Tail +25%
          specialEffects.leftParent.traitBoosts.Tail = 0.25
          break
        case EBreedingItem.CHIBIDEIS_CURSE: // Chibidei's Curse - Body +25%
          specialEffects.leftParent.traitBoosts.Body = 0.25
          break
        case EBreedingItem.ALL_SEEING_SEED: // All-seeing Seed - Eyes +25%
          specialEffects.leftParent.traitBoosts.Eyes = 0.25
          break
        case EBreedingItem.CHIM_LACS_CURIO: // Chim Lac's Curio - Beak +25%
          specialEffects.leftParent.traitBoosts.Beak = 0.25
          break
        case EBreedingItem.SUAVE_SCISSORS: // Suave Scissors - Comb +25%
          specialEffects.leftParent.traitBoosts.Comb = 0.25
          break
        case EBreedingItem.SIMURGHS_SOVEREIGN: // Simurgh's Sovereign - Wings +25%
          specialEffects.leftParent.traitBoosts.Wings = 0.25
          break
        case EBreedingItem.ST_ELMO_FIRE: // St. Elmo's Fire - Instinct +25%
          specialEffects.leftParent.traitBoosts.Instinct = 0.25
          break
        case EBreedingItem.RAINBOW_RAIN: // Rainbow Rain - Color +25%
          specialEffects.leftParent.traitBoosts.Color = 0.25
          break
        case EBreedingItem.DIPS_BEAK: // Dip's Beak - Attack IP +25%
          specialEffects.leftParent.ipBoosts['Innate Attack'] = 0.25
          break
        case EBreedingItem.POS2_PELLET: // Pos2 Pellet - Defense IP +25%
          specialEffects.leftParent.ipBoosts['Innate Defense'] = 0.25
          break
        case EBreedingItem.FETZZZ_FEET: // Fetzzz Feet - Speed IP +25%
          specialEffects.leftParent.ipBoosts['Innate Speed'] = 0.25
          break
        case EBreedingItem.VANDERERENS_VITALITY: // Vananderen's Vitality - Health IP +25%
          specialEffects.leftParent.ipBoosts['Innate Health'] = 0.25
          break
        case EBreedingItem.OUCHIES_ORNAMENT: // Ouchie's Ornament - Ferocity influence
          specialEffects.leftParent.ipBoosts['Innate Ferocity'] = 0.25
          break
        case EBreedingItem.PINONGS_BIRD: // Pinong's Bird - Cockrage influence
          specialEffects.leftParent.ipBoosts['Innate Cockrage'] = 0.25
          break
        case EBreedingItem.LOCKED_IN_STATE: // Lockedin State - Evasion influence
          specialEffects.leftParent.ipBoosts['Innate Evasion'] = 0.25
          break
      }
    })
  }

  if (breedingItems[1]) {
    breedingItems[1].forEach((itemId) => {
      const item = SABONG_CONFIG.BREEDING_ITEMS.find((i) => i.tokenId === itemId)
      if (!item) return

      switch (itemId) {
        case EBreedingItem.COMMON_INCUBATOR: // Common Incubator - 25% hatch time reduction
          specialEffects.global.hatchTimeReduction = Math.max(
            specialEffects.global.hatchTimeReduction,
            0.25
          )
          break
        case EBreedingItem.ARCANE_INCUBATOR: // Arcane Incubator - Instant hatch
          specialEffects.global.instantHatch = true
          break
        case EBreedingItem.SELENIUM: // Selenium - 25% breed time reduction
          specialEffects.global.breedTimeReduction = Math.max(
            specialEffects.global.breedTimeReduction,
            0.25
          )
          break
        case EBreedingItem.SELENIUM_PRO: // Selenium Pro - Instant breed
          specialEffects.global.instantBreed = true
          break
      }
    })
  }

  // Apply items to right parent
  if (breedingItems[2]) {
    breedingItems[2].forEach((itemId) => {
      const item = SABONG_CONFIG.BREEDING_ITEMS.find((i) => i.tokenId === itemId)
      if (!item) return

      switch (itemId) {
        case EBreedingItem.SOULKNOT: // Soulknot
          specialEffects.rightParent.soulknotActive = true
          break
        case EBreedingItem.RANDOMIZER: // Randomizer
          specialEffects.rightParent.randomizerActive = true
          break
        case EBreedingItem.GREGORS_GIFT: // Gregor's Gift - primary genes +25%
          specialEffects.rightParent.traitBoosts.primary = 0.25
          break
        case EBreedingItem.MENDELS_MEMENTO: // Mendel's Memento - recessive gene +25%
          specialEffects.rightParent.traitBoosts.recessive = 0.25
          break
        case EBreedingItem.QUENTINS_TALON: // Quentin's Talon - Feet +25%
          specialEffects.rightParent.traitBoosts.Feet = 0.25
          break
        case EBreedingItem.DRAGONS_WHIP: // Dragon's Whip - Tail +25%
          specialEffects.rightParent.traitBoosts.Tail = 0.25
          break
        case EBreedingItem.CHIBIDEIS_CURSE: // Chibidei's Curse - Body +25%
          specialEffects.rightParent.traitBoosts.Body = 0.25
          break
        case EBreedingItem.ALL_SEEING_SEED: // All-seeing Seed - Eyes +25%
          specialEffects.rightParent.traitBoosts.Eyes = 0.25
          break
        case EBreedingItem.CHIM_LACS_CURIO: // Chim Lac's Curio - Beak +25%
          specialEffects.rightParent.traitBoosts.Beak = 0.25
          break
        case EBreedingItem.SUAVE_SCISSORS: // Suave Scissors - Comb +25%
          specialEffects.rightParent.traitBoosts.Comb = 0.25
          break
        case EBreedingItem.SIMURGHS_SOVEREIGN: // Simurgh's Sovereign - Wings +25%
          specialEffects.rightParent.traitBoosts.Wings = 0.25
          break
        case EBreedingItem.ST_ELMO_FIRE: // St. Elmo's Fire - Instinct +25%
          specialEffects.rightParent.traitBoosts.Instinct = 0.25
          break
        case EBreedingItem.RAINBOW_RAIN: // Rainbow Rain - Color +25%
          specialEffects.rightParent.traitBoosts.Color = 0.25
          break
        case EBreedingItem.DIPS_BEAK: // Dip's Beak - Attack IP +25%
          specialEffects.rightParent.ipBoosts['Innate Attack'] = 0.25
          break
        case EBreedingItem.POS2_PELLET: // Pos2 Pellet - Defense IP +25%
          specialEffects.rightParent.ipBoosts['Innate Defense'] = 0.25
          break
        case EBreedingItem.FETZZZ_FEET: // Fetzzz Feet - Speed IP +25%
          specialEffects.rightParent.ipBoosts['Innate Speed'] = 0.25
          break
        case EBreedingItem.VANDERERENS_VITALITY: // Vananderen's Vitality - Health IP +25%
          specialEffects.rightParent.ipBoosts['Innate Health'] = 0.25
          break
        case EBreedingItem.OUCHIES_ORNAMENT: // Ouchie's Ornament - Ferocity influence
          specialEffects.rightParent.ipBoosts['Innate Ferocity'] = 0.25
          break
        case EBreedingItem.PINONGS_BIRD: // Pinong's Bird - Cockrage influence
          specialEffects.rightParent.ipBoosts['Innate Cockrage'] = 0.25
          break
        case EBreedingItem.LOCKED_IN_STATE: // Lockedin State - Evasion influence
          specialEffects.rightParent.ipBoosts['Innate Evasion'] = 0.25
          break
      }
    })
  }

  return { modifiedLeft, modifiedRight, specialEffects }
}

// Modified trait selection considering which parent has the boost
function getRandomTraitWithParentBoost(
  traits: string[],
  category: string,
  specialEffects: SpecialEffects
): string {
  const leftBoost = {
    p:
      (specialEffects.leftParent.traitBoosts.primary || 0) +
      (specialEffects.leftParent.traitBoosts[category] || 0),
    h1:
      (specialEffects.leftParent.traitBoosts.recessive || 0) +
      (specialEffects.leftParent.traitBoosts[category] || 0),
    h2:
      (specialEffects.leftParent.traitBoosts.recessive || 0) +
      (specialEffects.leftParent.traitBoosts[category] || 0),
    h3:
      (specialEffects.leftParent.traitBoosts.recessive || 0) +
      (specialEffects.leftParent.traitBoosts[category] || 0),
  }

  const rightBoost = {
    p:
      (specialEffects.rightParent.traitBoosts.primary || 0) +
      (specialEffects.rightParent.traitBoosts[category] || 0),
    h1:
      (specialEffects.rightParent.traitBoosts.recessive || 0) +
      (specialEffects.rightParent.traitBoosts[category] || 0),
    h2:
      (specialEffects.rightParent.traitBoosts.recessive || 0) +
      (specialEffects.rightParent.traitBoosts[category] || 0),
    h3:
      (specialEffects.rightParent.traitBoosts.recessive || 0) +
      (specialEffects.rightParent.traitBoosts[category] || 0),
  }

  const weights = [...SABONG_CONFIG.BREEDING_CONFIG.TRAITS_WEIGHTS]

  // p
  if (leftBoost.p > rightBoost.p) {
    weights[0] += (leftBoost.p - rightBoost.p) * weights[0]
    weights[1] -= (leftBoost.p - rightBoost.p) * weights[1]
  } else if (leftBoost.p < rightBoost.p) {
    weights[0] -= (rightBoost.p - leftBoost.p) * weights[0]
    weights[1] += (rightBoost.p - leftBoost.p) * weights[1]
  }

  // h1
  if (leftBoost.h1 > rightBoost.h1) {
    weights[2] += (leftBoost.h1 - rightBoost.h1) * weights[2]
    weights[3] -= (leftBoost.h1 - rightBoost.h1) * weights[3]
  } else if (leftBoost.h1 < rightBoost.h1) {
    weights[2] -= (rightBoost.h1 - leftBoost.h1) * weights[2]
    weights[3] += (rightBoost.h1 - leftBoost.h1) * weights[3]
  }

  // h2
  if (leftBoost.h2 > rightBoost.h2) {
    weights[4] += (leftBoost.h2 - rightBoost.h2) * weights[4]
    weights[5] -= (leftBoost.h2 - rightBoost.h2) * weights[5]
  } else if (leftBoost.h2 < rightBoost.h2) {
    weights[4] -= (rightBoost.h2 - leftBoost.h2) * weights[4]
    weights[5] += (rightBoost.h2 - leftBoost.h2) * weights[5]
  }

  // h3
  if (leftBoost.h3 > rightBoost.h3) {
    weights[6] += (leftBoost.h3 - rightBoost.h3) * weights[6]
    weights[7] -= (leftBoost.h3 - rightBoost.h3) * weights[7]
  } else if (leftBoost.h3 < rightBoost.h3) {
    weights[6] -= (rightBoost.h3 - leftBoost.h3) * weights[6]
    weights[7] += (rightBoost.h3 - leftBoost.h3) * weights[7]
  }

  const totalWeight = weights.reduce((sum, weight) => sum + weight, 0)

  let random = Math.random() * totalWeight

  // Find which item corresponds to this random weight
  for (let i = 0; i < weights.length; i++) {
    if (random < weights[i]) {
      return traits[i]
    }
    random -= weights[i]
  }

  // Fallback to first trait (should never happen with proper weights)
  return traits[0]
}

// Modified IP generation with per-parent breeding items
const generateOffspringIPTraitsWithParentItems = (parent1Stats, parent2Stats, specialEffects) => {
  const ipTraits = [
    { trait_type: 'Innate Attack', value: 0, display_type: 'number' },
    { trait_type: 'Innate Defense', value: 0, display_type: 'number' },
    { trait_type: 'Innate Speed', value: 0, display_type: 'number' },
    { trait_type: 'Innate Health', value: 0, display_type: 'number' },
    { trait_type: 'Innate Ferocity', value: 0, display_type: 'number' },
    { trait_type: 'Innate Cockrage', value: 0, display_type: 'number' },
    { trait_type: 'Innate Evasion', value: 0, display_type: 'number' },
  ]

  // Check for Soulknot effects on either parent
  const leftSoulknot = specialEffects.leftParent.soulknotActive && Math.random() < 0.25
  const rightSoulknot = specialEffects.rightParent.soulknotActive && Math.random() < 0.25

  if (leftSoulknot || rightSoulknot) {
    const inheritIndices = [0, 1, 2, 3, 4, 5, 6].sort(() => Math.random() - 0.5).slice(0, 3)

    // Determine which parent to inherit from
    let parentToInherit

    if (leftSoulknot && rightSoulknot) {
      parentToInherit = Math.random() < 0.5 ? parent1Stats : parent2Stats
    } else if (leftSoulknot) {
      parentToInherit = parent1Stats
    } else {
      parentToInherit = parent2Stats
    }

    inheritIndices.forEach((index) => {
      ipTraits[index].value = parentToInherit[index].value
    })

    // Generate remaining traits normally
    ipTraits.forEach((trait, index) => {
      if (!inheritIndices.includes(index)) {
        const parent1Value = parent1Stats[index].value
        const parent2Value = parent2Stats[index].value

        // Check for IP-specific boosts on each parent
        const leftBoost = specialEffects.leftParent.ipBoosts[trait.trait_type] || 0
        const rightBoost = specialEffects.rightParent.ipBoosts[trait.trait_type] || 0

        let leftChance = 0.5 + leftBoost
        let rightChance = 0.5 + rightBoost

        // Normalize
        const total = leftChance + rightChance
        leftChance = leftChance / total

        trait.value = Math.random() < leftChance ? parent1Value : parent2Value

        const randomFactor = Math.floor(Math.random() * 26) - 15
        trait.value = Math.max(0, Math.min(40, trait.value + randomFactor))
      }
    })
  } else {
    // Normal IP generation with per-parent boosts
    ipTraits.forEach((trait, index) => {
      const parent1Value = parent1Stats[index].value
      const parent2Value = parent2Stats[index].value

      // Check for IP-specific boosts on each parent
      const leftBoost = specialEffects.leftParent.ipBoosts[trait.trait_type] || 0
      const rightBoost = specialEffects.rightParent.ipBoosts[trait.trait_type] || 0

      let leftChance = 0.5 + leftBoost
      let rightChance = 0.5 + rightBoost

      // Normalize
      const total = leftChance + rightChance
      leftChance = leftChance / total

      trait.value = Math.random() < leftChance ? parent1Value : parent2Value

      const randomFactor = Math.floor(Math.random() * 26) - 15
      trait.value = Math.max(0, Math.min(40, trait.value + randomFactor))
    })
  }

  return ipTraits
}

export async function breed(
  chickenLeft: Traits,
  chickenRight: Traits,
  newChickenTokenId: number,
  chickenLeftTokenId: number,
  chickenRightTokenId: number,
  generation: string = 'Gen 1',
  breedingItems: number[][] = [[], [], []] // [leftParentItems, bothParentsItems, rightParentItems]
): Promise<{ traits: Traits; genes: string; metadata: any }> {
  // Apply breeding items effects per parent
  const { modifiedLeft, modifiedRight, specialEffects } = applyBreedingItemsEffects(
    chickenLeft,
    chickenRight,
    breedingItems
  )

  const parent1Stats = extractIPStats(modifiedLeft)
  const parent2Stats = extractIPStats(modifiedRight)

  // Generate offspring IP traits with per-parent breeding items effects
  const offspringInnatePoints = generateOffspringIPTraitsWithParentItems(
    parent1Stats,
    parent2Stats,
    specialEffects
  )

  // Get parent instincts with per-parent boosts
  const parentInstincts = [modifiedLeft.Instinct, modifiedRight.Instinct].filter(Boolean)
  const leftInstinctBoost = specialEffects.leftParent.traitBoosts.Instinct || 0
  const rightInstinctBoost = specialEffects.rightParent.traitBoosts.Instinct || 0

  // Generate weighted instinct with per-parent boosts
  const offspringInstinct = generateWeightedInstinctWithParentBoost(
    parentInstincts,
    leftInstinctBoost,
    rightInstinctBoost
  )

  const offspring: Partial<
    Record<TraitCategory, { p: string; h1: string; h2: string; h3: string }>
  > = {}

  for (const category of Object.keys(modifiedLeft) as TraitCategory[]) {
    if (!['Feet', 'Tail', 'Body', 'Wings', 'Eyes', 'Beak', 'Comb', 'Color'].includes(category))
      continue

    // Check for Randomizer effect on either parent
    if (specialEffects.leftParent.randomizerActive || specialEffects.rightParent.randomizerActive) {
      const p = getRandomNonLegendaryTrait(category)
      const h1 = getRandomNonLegendaryTrait(category)
      const h2 = getRandomNonLegendaryTrait(category)
      const h3 = getRandomNonLegendaryTrait(category)

      offspring[category] = { p, h1, h2, h3 }
      continue
    }

    const traitsPool = [
      modifiedLeft[category].p,
      modifiedRight[category].p,
      modifiedLeft[category].h1,
      modifiedRight[category].h1,
      modifiedLeft[category].h2,
      modifiedRight[category].h2,
      modifiedLeft[category].h3,
      modifiedRight[category].h3,
    ].map((trait, _, original) =>
      legendaryTraitMappings[category].includes(trait)
        ? original.slice(2)[Math.floor(Math.random() * (original.length - 2))] // Only select from traits after index 1
        : trait
    )

    const p = getRandomTraitWithParentBoost(traitsPool, category, specialEffects)
    const h1 = getRandomTraitWithParentBoost(traitsPool, category, specialEffects)
    const h2 = getRandomTraitWithParentBoost(traitsPool, category, specialEffects)
    const h3 = getRandomNonLegendaryTrait(category)

    offspring[category] = { p, h1, h2, h3 }
  }

  const offspringForcedDNA = Object.entries(offspring).map(([trait, values]) => ({
    trait,
    value: values.p,
  }))

  const metadata = await generate(
    newChickenTokenId,
    offspringForcedDNA,
    chickenLeftTokenId,
    chickenRightTokenId,
    generation,
    offspring,
    offspringInstinct,
    offspringInnatePoints
  )

  const genes = metadata?.attributes.find((attr) => attr.trait_type === 'Genes').value!

  return {
    traits: {
      ...(offspring as Record<TraitCategory, { p: string; h1: string; h2: string; h3: string }>),
      ...offspringInnatePoints
        .map((t) => ({ [t.trait_type]: t.value }))
        .reduce((acc, cur) => ({ ...acc, ...cur }), {}),
      Instinct: offspringInstinct as string,
    } as Traits,
    genes,
    metadata,
  }
}

// Helper function for instinct generation with per-parent boosts
function generateWeightedInstinctWithParentBoost(
  parentInstincts: string[],
  leftBoost: number = 0,
  rightBoost: number = 0
) {
  const instincts = [
    'Aggressive',
    'Steadfast',
    'Swift',
    'Stalwart',
    'Balanced',
    'Reckless',
    'Resolute',
    'Elusive',
    'Tenacious',
    'Unyielding',
    'Vicious',
    'Adaptive',
    'Versatile',
    'Relentless',
    'Blazing',
    'Bulwark',
    'Enduring',
  ]

  const weights = new Map<string, number>()

  // Calculate base weight for non-parent instincts
  const totalInstincts = instincts.length
  const parentCount = parentInstincts.filter(
    (instinct) => instinct && instincts.includes(instinct)
  ).length
  const nonParentCount = totalInstincts - parentCount

  // Calculate parent weights
  const leftParentWeight =
    parentInstincts[0] && instincts.includes(parentInstincts[0]) ? 0.015 + leftBoost : 0.015
  const rightParentWeight =
    parentInstincts[1] && instincts.includes(parentInstincts[1]) ? 0.015 + rightBoost : 0.015
  const totalParentWeight = leftParentWeight + rightParentWeight

  // Calculate remaining weight for non-parent instincts
  const remainingWeight = 1 - totalParentWeight
  const baseWeight = nonParentCount > 0 ? remainingWeight / nonParentCount : 0

  // Set weights for all instincts
  instincts.forEach((instinct) => weights.set(instinct, baseWeight))

  // Add extra weight to parent instincts with individual boosts
  if (parentInstincts[0] && weights.has(parentInstincts[0])) {
    weights.set(parentInstincts[0], leftParentWeight)
  }
  if (parentInstincts[1] && weights.has(parentInstincts[1])) {
    weights.set(parentInstincts[1], rightParentWeight)
  }

  const totalWeight = Array.from(weights.values()).reduce((sum, weight) => sum + weight, 0)
  let random = Math.random() * totalWeight

  for (const [instinct, weight] of weights.entries()) {
    random -= weight
    if (random <= 0) {
      return instinct
    }
  }

  return instincts[0]
}
